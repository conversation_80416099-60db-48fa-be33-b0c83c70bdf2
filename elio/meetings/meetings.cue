package meetings

MarsPostgresOverride: string | *""

if #Meta.Environment.Type == "development" && #Meta.Environment.Cloud == "local" {
    // Local override as we're connecting to postgres provisioned from `docker-compose.yml`
    MarsPostgresOverride: "postgres://rumi:rumi@localhost:5432/rumi?search_path=mars&sslmode=disable"
}

Kafka: {
	TLS: bool | *false
	SASL: bool | *false
	TopicSessionsUpdates: string | *""
}
Nebula: {
    HostUrl: string | *"http://localhost:3006"
}
BaseURL: string | *"http://localhost:3000"
