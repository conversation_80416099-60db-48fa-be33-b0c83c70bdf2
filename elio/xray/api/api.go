package api

import (
	"encoding/json"
	"fmt"

	"encore.app/shared"
)

// X-Ray Creation Step 1

type GenerateXRayPromptRequest struct {
	Description string `json:"description" validate:"required"`
}

func (request GenerateXRayPromptRequest) Validate() error {
	err := shared.Validate.Struct(request)
	if err != nil {
		return err
	}
	return nil
}

type GenerateXRayPromptResponseData struct {
	Type 	string 	`json:"type"`
	Prompt 	string 	`json:"prompt"`
}

type GenerateXRayPromptResponse struct {
	Success bool                         	`json:"success"`
	Message string                       	`json:"message"`
	Data    *GenerateXRayPromptResponseData `json:"data,omitempty"`
}

// X-Ray Creation Step 2

type GenerateXRayInfoRequest struct {
	Type     string `json:"type" validate:"required"`
	Prompt   string `json:"prompt" validate:"required"`
}

func (request GenerateXRayInfoRequest) Validate() error {
	err := shared.Validate.Struct(request)
	if err != nil {
		return err
	}
	return nil
}

type GenerateXRayInfoResponseData struct {
	Title        string `json:"title"`
	Icon         string `json:"icon"`
	ShortSummary string `json:"shortSummary"`
}

type GenerateXRayInfoResponse struct {
	Success bool                        	`json:"success"`
	Message string                      	`json:"message"`
	Data    *GenerateXRayInfoResponseData 	`json:"data,omitempty"`
}

// X-Ray Creation Step 3

type CreateXRayRequest struct {
	Description  string `json:"description" validate:"required"`
	Type     	 string `json:"type" validate:"required"`
	Prompt       string `json:"prompt" validate:"required"`
	Title        string `json:"title" validate:"required"`
	Icon         string `json:"icon" validate:"required"`
	ShortSummary string `json:"shortSummary" validate:"required"`
	Frequency    string `json:"frequency,omitempty"`
	// TODO: Revisit this, probably shouldn't be map[string]bool
	AlertChannels map[string]bool `json:"alertChannels,omitempty"`
}

func (request CreateXRayRequest) Validate() error {
	err := shared.Validate.Struct(request)
	if err != nil {
		return err
	}
	return nil
}

type XRayDocCommit struct {
	ID        int    `json:"id"`
	XrayID    int    `json:"xrayId"`
	Content   string `json:"content"`
	AuthorID  int    `json:"authorId"`
	CreatedAt int    `json:"createdAt"`
	UpdatedAt int    `json:"updatedAt"`
}

type XRayDTO struct {
	ID                       int             `json:"id"`
	OwnerID                  int             `json:"ownerId"`
	Title                    string          `json:"title"`
	Description              string          `json:"description"`
	Prompt                   string          `json:"prompt"`
	Icon                     string          `json:"icon"`
	ShortSummary             string          `json:"shortSummary"`
	CurrentCommitID          *int            `json:"currentCommitId,omitempty"`
	CurrentCommit            *XRayDocCommit  `json:"currentCommit,omitempty"`
	AlertChannels            map[string]bool `json:"alertChannels"`
	IsActive                 bool            `json:"isActive"`
	Visibility               string          `json:"visibility"`
	Type                 	 string          `json:"type"`
	Scope                    string          `json:"scope"`
	UnreadNotificationsCount *int            `json:"unreadNotificationsCount,omitempty"`
	Frequency                *string         `json:"frequency,omitempty"`
	LastDigestAt             *int            `json:"lastDigestAt,omitempty"`
	CreatedAt                int             `json:"createdAt"`
	UpdatedAt                int             `json:"updatedAt"`
}


type CreateXRayResponse struct {
	Success bool                        `json:"success"`
	Message string                      `json:"message"`
	Data    *XRayDTO 	`json:"data,omitempty"`
}

// List X-Rays

type ListXRaysRequest struct {
	Limit      int    `query:"limit"`
	Offset     int    `query:"offset"`
	TypeFilter string `query:"type_filter"`
	SortBy     string `query:"sort_by"`
}

func (request ListXRaysRequest) Validate() error {
	if request.Limit != 0 {
		if request.Limit < 1 || request.Limit > 100 {
			return fmt.Errorf("limit must be between 1 and 100")
		}
	}
	if request.Offset < 0 {
		return fmt.Errorf("offset must be >= 0")
	}
	if request.TypeFilter != "" {
		validTypes := map[string]bool{"build": true, "monitor": true, "digest": true}
		if !validTypes[request.TypeFilter] {
			return fmt.Errorf("type_filter must be one of: build, monitor, digest")
		}
	}
	if request.SortBy != "" {
		validSorts := map[string]bool{"created_at": true, "updated_at": true, "last_updated": true, "title": true}
		if !validSorts[request.SortBy] {
			return fmt.Errorf("sort_by must be one of: created_at, updated_at, last_updated, title")
		}
	}
	return nil
}

type ListXRaysResponseData struct {
	Xrays   	[]XRayDTO 	`json:"xrays"`
	TotalCount  int       	`json:"totalCount"`
	HasMore 	bool      	`json:"hasMore"`
}

type ListXRaysResponse struct {
	Success bool                   `json:"success"`
	Message string                 `json:"message"`
	Data    *ListXRaysResponseData `json:"data,omitempty"`
}

// Get X-Ray by ID


type GetXRayResponse struct {
	Success bool                 `json:"success"`
	Message string               `json:"message"`
	Data    *XRayDTO `json:"data,omitempty"`
}

type XRayTemplateDTO struct {
	ID           int             `json:"id"`
	OwnerID      int             `json:"ownerId"`
	Title        string          `json:"title"`
	Description  string          `json:"description"`
	Prompt       string          `json:"prompt"`
	Icon         string          `json:"icon"`
	ShortSummary string          `json:"shortSummary"`
	Type     string          	 `json:"type"`
	CreatedAt    int             `json:"createdAt"`
	UpdatedAt    int             `json:"updatedAt"`
	Owner        *shared.UserDTO `json:"owner,omitempty"`
}

type ListXRayTemplatesResponseData struct {
	Templates 	[]XRayTemplateDTO `json:"templates"`
	TotalCount  int               `json:"totalCount"`
	HasMore   	bool              `json:"hasMore"`
}

type ListXRayTemplatesResponse struct {
	Success bool                           `json:"success"`
	Message string                         `json:"message"`
	Data    *ListXRayTemplatesResponseData `json:"data,omitempty"`
}

// X-Ray Notifications

type GetXRayNotificationsRequest struct {
	Limit  int `query:"limit"`
	Offset int `query:"offset"`
}

func (request GetXRayNotificationsRequest) Validate() error {
	if request.Limit != 0 {
		if request.Limit < 1 || request.Limit > 100 {
			return fmt.Errorf("limit must be between 1 and 100")
		}
	}
	if request.Offset < 0 {
		return fmt.Errorf("offset must be >= 0")
	}
	return nil
}

type XRayNotificationDTO struct {
	ID              int              `json:"id"`
	XrayDocCommitID int              `json:"xrayDocCommitId"`
	UserID          int              `json:"userId"`
	Seen            bool             `json:"seen"`
	Content         string           `json:"content"`
	CreatedAt       int              `json:"createdAt"`
	UpdatedAt       int              `json:"updatedAt"`
	Source          *json.RawMessage `json:"source"`
}

type GetXRayNotificationsResponseData struct {
	Notifications []XRayNotificationDTO `json:"notifications"`
	TotalCount    int                   `json:"totalCount"`
	HasMore       bool                  `json:"hasMore"`
}

type GetXRayNotificationsResponse struct {
	Success bool                              `json:"success"`
	Message string                            `json:"message"`
	Data    *GetXRayNotificationsResponseData `json:"data,omitempty"`
}

// Mark X-Ray Notifications as Seen

type MarkXRayNotificationsSeenResponseData struct {
	MarkedCount int `json:"markedCount"`
}

type MarkXRayNotificationsSeenResponse struct {
	Success bool                                   `json:"success"`
	Message string                                 `json:"message"`
	Data    *MarkXRayNotificationsSeenResponseData `json:"data,omitempty"`
}

// Update X-Ray

type UpdateXRayRequest struct {
	Title         *string          `json:"title,omitempty"`
	Icon          *string          `json:"icon,omitempty"`
	Prompt        *string          `json:"prompt,omitempty"`
	AlertChannels *map[string]bool `json:"alertChannels,omitempty"`
	IsActive      *bool            `json:"isActive,omitempty"`
	Frequency     *string          `json:"frequency,omitempty"`
}

func (request UpdateXRayRequest) Validate() error {
	// Add any validation logic here if needed
	return nil
}

// Delete X-Ray

type DeleteXRayResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// Get X-Ray Template


type GetXRayTemplateResponse struct {
	Success bool                         `json:"success"`
	Message string                       `json:"message"`
	Data    *XRayTemplateDTO `json:"data,omitempty"`
}
