package hubble

Emails: {
	TransactionalEmailAddress: string | *"<EMAIL>"
	MarketingEmailAddress: string | *"<EMAIL>"
}
Mars: {
	URL: string | *"http://localhost:3001"
	MarsDatabase: string | *"mars"
}
Aurora: {
	URL: string | *"https://aurora.develop.waitroom.com"
}
Mailjet: {
	WaitroomListID: string | *"00000"
}
BotUsers: {
	UserIDs: string | *""
}
Postmark: {
  OTPFromEmail: string | *"<EMAIL>"
  APIBaseURL: string | *"https://api.postmarkapp.com"
}

MarsPostgresOverride: string | *""
HubblePostgresOverride: string | *""

if #Meta.Environment.Type == "development" && #Meta.Environment.Cloud == "local" {
    // Local override as we're connecting to postgres provisioned from `docker-compose.yml`
    MarsPostgresOverride: "postgres://rumi:rumi@localhost:5432/rumi?search_path=mars&sslmode=disable"
}
