from temporalio import activity
from typing import Any, Dict, List
from nebula.temporal.types import (
    XRayQuickScanActivityParams,
    XRayQuickScanResult,
    XRayDeepScanParams,
    XRayDeepScanActivityResult,
)
from nebula.services.x_ray.types import XRayModel


class MockXRayActivities:
    """Mock X-Ray activities for testing"""

    @activity.defn
    async def get_xrays(self, user_id: int) -> List[XRayModel]:
        """Mock implementation of get_xrays"""
        return [
            XRayModel(
                id=1,
                owner_id=user_id,
                title="Weekly Status Monitor",
                description="Track team progress and blockers from weekly meetings",
                prompt="Extract status updates, blockers, and action items",
                icon="📊",
                short_summary="Monitor weekly team status updates",
                xray_type="monitor",
                scope="all",
                visibility="user",
                is_active=True,
                current_commit_id=1,
                created_at="2024-01-01T00:00:00Z",
                updated_at="2024-01-01T00:00:00Z",
                last_digest_at=None,  # Not a digest type
            ),
            XRayModel(
                id=2,
                owner_id=user_id,
                title="Project Requirements Build",
                description="Build comprehensive project requirements document",
                prompt="Extract and organize project requirements, features, and specifications",
                icon="🏗️",
                short_summary="Build project requirements documentation",
                xray_type="build",
                scope="all",
                visibility="user",
                is_active=True,
                current_commit_id=2,
                created_at="2024-01-01T00:00:00Z",
                updated_at="2024-01-01T00:00:00Z",
                last_digest_at=None,  # Not a digest type
            ),
        ]

    @activity.defn
    async def get_transcript_by_recurrence_id(self, recurrence_id: int) -> str:
        """Mock implementation of get_transcript_by_recurrence_id"""
        return f"Mock transcript for recurrence {recurrence_id}. We discussed project status, identified some blockers, and assigned action items for next week."

    @activity.defn
    async def quick_scan(
        self, params: XRayQuickScanActivityParams
    ) -> XRayQuickScanResult:
        """Mock implementation of quick_scan"""
        # Return first doc as relevant for testing
        if params.xrays:
            return XRayQuickScanResult(xray_ids=[params.xrays[0].id])
        return XRayQuickScanResult(xray_ids=[])

    @activity.defn
    async def get_xray_by_id(self, doc_id: int) -> XRayModel | None:
        """Mock implementation of get_xray_by_id"""
        # Return None for invalid doc_id to test the null case
        if doc_id <= 0:
            return None

        return XRayModel(
            id=doc_id,
            owner_id=12345,
            title="Weekly Status Monitor",
            description="Track team progress and blockers from weekly meetings",
            prompt="Extract status updates, blockers, and action items",
            icon="📊",
            short_summary="Monitor weekly team status updates",
            xray_type="monitor",
            scope="all",
            visibility="user",
            is_active=True,
            current_commit_id=1,
            created_at="2024-01-01T00:00:00Z",
            updated_at="2024-01-01T00:00:00Z",
            last_digest_at=None,  # Not a digest type
        )

    @activity.defn
    async def deep_scan(self, params: XRayDeepScanParams) -> XRayDeepScanActivityResult:
        """Mock implementation of deep_scan"""
        return XRayDeepScanActivityResult(
            success=True,
            changed=True,
            message="Successfully updated document with new content",
            prev_commit_content="Previous content",
            new_commit_content="Updated content with new meeting insights",
        )

    # Backfill workflow activities
    @activity.defn
    async def get_user_by_id(self, user_id: int) -> Dict[str, Any] | None:
        """Mock implementation of get_user_by_id"""
        if user_id <= 0:
            return None
        
        # Create team object if user has a team (mimicking Elio response structure)
        team = None
        if user_id == 12345:  # This user has a team
            team = {
                "id": "100",
                "name": "Test Team",
                "domains": ["example.com"],
                "role": "member",
                "createdAt": 1640995200,
                "updatedAt": 1640995200,
            }
        
        return {
            "userId": user_id,  # Use userId to match database schema
            "id": str(user_id),  # Also include id for compatibility
            "firstName": "John",
            "lastName": "Doe",
            "email": f"user{user_id}@example.com",
            "teamId": 100 if user_id == 12345 else None,
            "team": team,  # Nested team object like Elio response
        }

    @activity.defn
    async def get_past_user_session_recurrences_by_user_id(
        self, user_id: int
    ) -> List[Dict[str, Any]]:
        """Mock implementation of get_past_user_session_recurrences_by_user_id"""
        return [
            {
                "recurrenceID": 1001,
                "sessionID": 2001,
                "title": "Weekly Team Standup",
                "startTimestamp": 1640995200,  # 2022-01-01 00:00:00
                "endTimestamp": 1640998800,  # 2022-01-01 01:00:00
                "createdAt": 1640995200,
            },
            {
                "recurrenceID": 1002,
                "sessionID": 2002,
                "title": "Project Review Meeting",
                "startTimestamp": 1641081600,  # 2022-01-02 00:00:00
                "endTimestamp": 1641085200,  # 2022-01-02 01:00:00
                "createdAt": 1641081600,
            },
        ]
