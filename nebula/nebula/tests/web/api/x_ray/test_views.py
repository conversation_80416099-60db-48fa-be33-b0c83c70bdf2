import pytest
from unittest.mock import AsyncMock, MagicMock
from httpx import AsyncClient, ASGITransport
from datetime import datetime
import types

from nebula.web.application import get_app
from nebula.web.api.auth import authorize
from nebula.services.x_ray.xray_service import (
    XRayService,
    XRayDescriptionGenerationResult,
    XRayMetadataGenerationResult,
)
from nebula.web.api.x_ray.views import get_xray_service


class TestXRayViews:
    """Integration tests for X-Ray API endpoints"""

    @pytest.fixture
    def app(self):
        """Create FastAPI application for testing"""
        app = get_app()
        # Override auth dependency for testing
        app.dependency_overrides[authorize] = lambda: None
        return app

    @pytest.fixture
    def test_user_id(self):
        """Test user ID"""
        return "12345"

    @pytest.fixture
    def mock_service(self):
        """Create a mock XRayService"""
        service = MagicMock(spec=XRayService)
        service.get_xrays_paginated = AsyncMock()
        service.get_xray_templates_paginated = AsyncMock()
        service.update_xray = AsyncMock()
        service.get_xray_by_id = AsyncMock()
        service.generate_step1_type_and_prompt = AsyncMock()
        service.generate_step2_metadata = AsyncMock()
        service.create_xray = AsyncMock()
        return service

    @pytest.mark.asyncio
    async def test_create_xray_step1_success(self, app, test_user_id, mock_service):
        """Test successful X-Ray creation step 1"""
        # Mock service response
        mock_result = XRayDescriptionGenerationResult(
            xray_type="build",
            prompt="Extract project updates and status changes from meeting discussions.",
        )
        mock_service.generate_step1_type_and_prompt.return_value = mock_result

        # Override the dependency injection
        app.dependency_overrides[get_xray_service] = lambda: mock_service

        request_data = {
            "description": "I want to track project progress and status updates from our weekly team meetings"
        }

        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.post(
                "/v1.0/x-ray/xrays/create/step1",
                json=request_data,
                headers={"x-user-id": test_user_id},
            )

            assert response.status_code == 200
            data = response.json()

            assert data["success"] is True
            assert data["message"] == "Success"
            assert "data" in data
            assert data["data"]["xrayType"] == "build"
            assert "project updates" in data["data"]["prompt"]

        # Verify service was called with correct description
        mock_service.generate_step1_type_and_prompt.assert_called_once_with(
            request_data["description"]
        )

    @pytest.mark.asyncio
    async def test_create_xray_step1_monitor_type(
        self, app, test_user_id, mock_service
    ):
        """Test X-Ray creation step 1 with monitor type"""
        mock_result = XRayDescriptionGenerationResult(
            xray_type="monitor",
            prompt="Track specific risks and issues mentioned in meetings.",
        )
        mock_service.generate_step1_type_and_prompt.return_value = mock_result

        app.dependency_overrides[get_xray_service] = lambda: mock_service

        request_data = {
            "description": "Monitor risks and issues discussed in our meetings"
        }

        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.post(
                "/v1.0/x-ray/xrays/create/step1",
                json=request_data,
                headers={"x-user-id": test_user_id},
            )

            assert response.status_code == 200
            data = response.json()
            assert data["data"]["xrayType"] == "monitor"

    @pytest.mark.asyncio
    async def test_create_xray_step1_digest_type(self, app, test_user_id, mock_service):
        """Test X-Ray creation step 1 with digest type"""
        mock_result = XRayDescriptionGenerationResult(
            xray_type="digest",
            prompt="Provide weekly summaries of team meeting discussions.",
        )
        mock_service.generate_step1_type_and_prompt.return_value = mock_result

        app.dependency_overrides[get_xray_service] = lambda: mock_service

        request_data = {"description": "Generate weekly summaries of our team meetings"}

        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.post(
                "/v1.0/x-ray/xrays/create/step1",
                json=request_data,
                headers={"x-user-id": test_user_id},
            )

            assert response.status_code == 200
            data = response.json()
            assert data["data"]["xrayType"] == "digest"

    @pytest.mark.asyncio
    async def test_create_xray_step1_validation_error(
        self, app, test_user_id, mock_service
    ):
        """Test X-Ray creation step 1 with validation error"""
        mock_service.generate_step1_type_and_prompt.side_effect = ValueError(
            "Invalid input"
        )

        app.dependency_overrides[get_xray_service] = lambda: mock_service

        request_data = {"description": "Invalid description"}

        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.post(
                "/v1.0/x-ray/xrays/create/step1",
                json=request_data,
                headers={"x-user-id": test_user_id},
            )

            assert response.status_code == 422
            data = response.json()
            assert "detail" in data
            # Check if detail is a string or a list
            if isinstance(data["detail"], str):
                assert "Invalid input" in data["detail"]
            else:
                assert any(
                    "Invalid input" in err.get("msg", "") for err in data["detail"]
                )

    @pytest.mark.asyncio
    async def test_create_xray_step1_service_error(
        self, app, test_user_id, mock_service
    ):
        """Test X-Ray creation step 1 with service error"""
        mock_service.generate_step1_type_and_prompt.side_effect = Exception(
            "OpenAI API error"
        )

        app.dependency_overrides[get_xray_service] = lambda: mock_service

        request_data = {"description": "Test description"}

        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.post(
                "/v1.0/x-ray/xrays/create/step1",
                json=request_data,
                headers={"x-user-id": test_user_id},
            )

            assert response.status_code == 500
            data = response.json()
            assert "detail" in data
            assert "Internal server error" in data["detail"]

    @pytest.mark.asyncio
    async def test_create_xray_step1_missing_description(self, app, test_user_id):
        """Test X-Ray creation step 1 with missing description"""
        request_data = {}  # Missing description

        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.post(
                "/v1.0/x-ray/xrays/create/step1",
                json=request_data,
                headers={"x-user-id": test_user_id},
            )

            assert response.status_code == 422  # Validation error

    @pytest.mark.asyncio
    async def test_create_xray_step1_empty_description(self, app, test_user_id):
        """Test X-Ray creation step 1 with empty description"""
        request_data = {"description": ""}

        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.post(
                "/v1.0/x-ray/xrays/create/step1",
                json=request_data,
                headers={"x-user-id": test_user_id},
            )

            assert (
                response.status_code == 200
            )  # Endpoint currently returns 200 for empty description

    @pytest.mark.asyncio
    async def test_create_xray_step1_unauthorized(self, app):
        """Test X-Ray creation step 1 without user ID header"""
        request_data = {"description": "Test description"}

        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.post(
                "/v1.0/x-ray/xrays/create/step1", json=request_data
            )

            assert response.status_code == 422  # Missing required header

    @pytest.mark.asyncio
    async def test_list_xrays_success(self, app, test_user_id, mock_service):
        """Test successful X-Ray listing"""
        # Create a mock X-Ray object with actual values
        mock_xray = {
            "id": 1,
            "ownerId": 12345,
            "title": "Test X-Ray",
            "description": "Test description",
            "prompt": "Test prompt",
            "icon": "📊",
            "shortSummary": "Test summary",
            "alertChannels": {"slack": True},
            "isActive": True,
            "visibility": "private",
            "xrayType": "build",
            "scope": "team",
            "createdAt": datetime.now(),
            "updatedAt": datetime.now(),
        }

        mock_service.get_xrays_paginated.return_value = (
            [mock_xray],
            1,  # total
            False,  # has_more
        )

        app.dependency_overrides[get_xray_service] = lambda: mock_service

        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.get(
                "/v1.0/x-ray/xrays",
                headers={"x-user-id": test_user_id},
            )

            assert response.status_code == 200
            data = response.json()

            assert data["success"] is True
            assert data["message"] == "Success"
            assert "data" in data
            assert len(data["data"]["xrays"]) == 1
            assert data["data"]["total"] == 1
            assert data["data"]["hasMore"] is False

        # Verify service was called with correct parameters
        mock_service.get_xrays_paginated.assert_called_once_with(
            user_id=int(test_user_id),
            limit=10,  # default limit
            skip=0,  # default offset
            xray_type=None,  # default type filter
            sort_by="last_updated",  # default sort
        )

    @pytest.mark.asyncio
    async def test_list_xray_templates_success(self, app, test_user_id, mock_service):
        """Test successful X-Ray template listing"""
        # Create a mock template object with actual values
        mock_template = {
            "id": 1,
            "ownerId": None,
            "title": "Test Template",
            "description": "Test description",
            "prompt": "Test prompt",
            "icon": "📊",
            "shortSummary": "Test summary",
            "xrayType": "build",
            "createdAt": datetime.now(),
            "updatedAt": datetime.now(),
        }

        mock_service.get_xray_templates_paginated.return_value = (
            [mock_template],
            1,  # total
            False,  # has_more
        )

        app.dependency_overrides[get_xray_service] = lambda: mock_service

        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.get(
                "/v1.0/x-ray/xray-templates",
                headers={"x-user-id": test_user_id},
            )

            assert response.status_code == 200
            data = response.json()

            assert data["success"] is True
            assert data["message"] == "Success"
            assert "data" in data
            assert len(data["data"]["templates"]) == 1
            assert data["data"]["total"] == 1
            assert data["data"]["hasMore"] is False

        # Verify service was called with correct parameters
        mock_service.get_xray_templates_paginated.assert_called_once_with(
            limit=10,  # default limit
            skip=0,  # default offset
        )

    @pytest.mark.asyncio
    async def test_patch_xray_success(self, app, test_user_id, mock_service):
        """Test successful X-Ray update"""
        # Create a mock X-Ray object with actual values
        mock_xray = {
            "id": 1,
            "ownerId": 12345,
            "title": "Updated X-Ray",
            "description": "Test description",
            "prompt": "Test prompt",
            "icon": "📊",
            "shortSummary": "Test summary",
            "alertChannels": {"slack": True},
            "isActive": True,
            "visibility": "private",
            "xrayType": "build",
            "scope": "team",
            "createdAt": datetime.now(),
            "updatedAt": datetime.now(),
        }

        mock_service.update_xray.return_value = mock_xray

        app.dependency_overrides[get_xray_service] = lambda: mock_service

        request_data = {
            "title": "Updated X-Ray",
            "alertChannels": {"slack": True},
            "isActive": True,
        }

        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.patch(
                "/v1.0/x-ray/xrays/1",
                json=request_data,
                headers={"x-user-id": test_user_id},
            )

            assert response.status_code == 200
            data = response.json()

            assert data["success"] is True
            assert data["message"] == "Success"
            assert "data" in data
            assert data["data"]["xray"]["title"] == "Updated X-Ray"

        # Verify service was called with correct parameters
        mock_service.update_xray.assert_called_once()
        call_args = mock_service.update_xray.call_args
        assert call_args[1]["xray_id"] == 1
        assert call_args[1]["user_id"] == int(test_user_id)
        # Check that update_data is XRayUpdateData instance
        update_data = call_args[1]["update_data"]
        assert update_data.title == "Updated X-Ray"
        assert update_data.alert_channels == {"slack": True}
        assert update_data.is_active is True

    @pytest.mark.asyncio
    async def test_patch_xray_not_found(self, app, test_user_id, mock_service):
        """Test X-Ray update with non-existent X-Ray"""
        mock_service.update_xray.return_value = None

        app.dependency_overrides[get_xray_service] = lambda: mock_service

        request_data = {
            "title": "Updated X-Ray",
            "alertChannels": {"slack": True},
            "isActive": True,
        }

        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.patch(
                "/v1.0/x-ray/xrays/999",
                json=request_data,
                headers={"x-user-id": test_user_id},
            )

            assert response.status_code == 404
            data = response.json()
            assert data["detail"] == "X-Ray not found"

    @pytest.mark.asyncio
    async def test_list_xrays_unauthorized(self, app):
        """Test X-Ray listing without user ID header"""
        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.get("/v1.0/x-ray/xrays")

            assert response.status_code == 422
            data = response.json()
            assert "detail" in data
            assert any("Field required" in err.get("msg", "") for err in data["detail"])

    @pytest.mark.asyncio
    async def test_invalid_query_parameters(self, app, test_user_id):
        """Test X-Ray listing with invalid query parameters"""
        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.get(
                "/v1.0/x-ray/xrays?limit=0",  # Invalid limit
                headers={"x-user-id": test_user_id},
            )

            assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_create_xray_step2_success(self, app, test_user_id, mock_service):
        """Test successful metadata generation in step 2"""
        mock_service.generate_step2_metadata.return_value = (
            XRayMetadataGenerationResult(
                title="Test Title",
                emoji="🔍",
                short_summary="This is a test summary",
            )
        )

        response = await self.create_xray_step2(app, test_user_id, mock_service)

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "Success"
        assert data["data"]["title"] == "Test Title"
        assert data["data"]["emoji"] == "🔍"
        assert data["data"]["shortSummary"] == "This is a test summary"

    @pytest.mark.asyncio
    async def test_create_xray_step2_validation_error(
        self, app, test_user_id, mock_service
    ):
        """Test validation error in step 2"""
        mock_service.generate_step2_metadata.side_effect = ValueError("Invalid input")

        response = await self.create_xray_step2(app, test_user_id, mock_service)

        assert response.status_code == 422
        data = response.json()
        assert data["detail"] == "Invalid input"

    @pytest.mark.asyncio
    async def test_create_xray_step2_service_error(
        self, app, test_user_id, mock_service
    ):
        """Test service error in step 2"""
        mock_service.generate_step2_metadata.side_effect = Exception("Service error")

        response = await self.create_xray_step2(app, test_user_id, mock_service)

        assert response.status_code == 500
        data = response.json()
        assert data["detail"] == "Internal server error"

    @pytest.mark.asyncio
    async def test_create_xray_step2_missing_user_id(
        self, app, test_user_id, mock_service
    ):
        """Test missing user ID in step 2"""
        response = await self.create_xray_step2(
            app, test_user_id, mock_service, include_user_id=False
        )

        assert response.status_code == 422
        data = response.json()
        # FastAPI returns a list of validation errors for missing required headers
        assert isinstance(data["detail"], list)
        assert any(
            err["loc"] == ["header", "x-user-id"] and err["msg"] == "Field required"
            for err in data["detail"]
        )

    async def create_xray_step2(
        self, app, test_user_id, mock_service, include_user_id=True
    ):
        """Helper method to create step 2 of X-Ray"""
        # Override the dependency injection
        app.dependency_overrides[get_xray_service] = lambda: mock_service

        request_data = {
            "xrayType": "monitor",
            "prompt": "Test prompt",
        }

        headers = {"x-user-id": test_user_id} if include_user_id else {}

        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.post(
                "/v1.0/x-ray/xrays/create/step2",
                json=request_data,
                headers=headers,
            )

            return response

    @pytest.mark.asyncio
    async def test_create_xray_step3_success(self, app, test_user_id, mock_service):
        """Test successful X-Ray creation in step 3"""
        # Mock the created X-Ray using SimpleNamespace
        mock_xray = types.SimpleNamespace(
            id=1,
            ownerId=int(test_user_id),
            title="Test Title",
            description="Test Description",
            prompt="Test Prompt",
            icon="🔍",
            shortSummary="Test Summary",
            alertChannels={},
            isActive=True,
            visibility="user",
            xrayType="monitor",
            scope="all",
            createdAt=datetime.now(),
            updatedAt=datetime.now(),
        )
        mock_service.create_xray.return_value = mock_xray

        response = await self.create_xray_step3(app, test_user_id, mock_service)

        if response.status_code != 200:
            print("RESPONSE BODY:", response.text)
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "Success"
        assert data["data"]["xray"]["id"] == 1
        assert data["data"]["xray"]["title"] == "Test Title"
        assert data["data"]["xray"]["description"] == "Test Description"
        assert data["data"]["xray"]["prompt"] == "Test Prompt"
        assert data["data"]["xray"]["icon"] == "🔍"
        assert data["data"]["xray"]["shortSummary"] == "Test Summary"
        assert data["data"]["xray"]["xrayType"] == "monitor"

    @pytest.mark.asyncio
    async def test_create_xray_step3_validation_error(
        self, app, test_user_id, mock_service
    ):
        """Test validation error in step 3"""
        mock_service.create_xray.side_effect = ValueError("Invalid input")

        response = await self.create_xray_step3(app, test_user_id, mock_service)

        assert response.status_code == 422
        data = response.json()
        assert data["detail"] == "Invalid input"

    @pytest.mark.asyncio
    async def test_create_xray_step3_service_error(
        self, app, test_user_id, mock_service
    ):
        """Test service error in step 3"""
        mock_service.create_xray.side_effect = Exception("Service error")

        response = await self.create_xray_step3(app, test_user_id, mock_service)

        assert response.status_code == 500
        data = response.json()
        assert data["detail"] == "Internal server error"

    @pytest.mark.asyncio
    async def test_create_xray_step3_missing_user_id(
        self, app, test_user_id, mock_service
    ):
        """Test missing user ID in step 3"""
        response = await self.create_xray_step3(
            app, test_user_id, mock_service, include_user_id=False
        )

        assert response.status_code == 422
        data = response.json()
        assert isinstance(data["detail"], list)
        assert any(
            err["loc"] == ["header", "x-user-id"] and err["msg"] == "Field required"
            for err in data["detail"]
        )

    async def create_xray_step3(
        self, app, test_user_id, mock_service, include_user_id=True
    ):
        """Helper method to create step 3 of X-Ray"""
        # Override the dependency injection
        app.dependency_overrides[get_xray_service] = lambda: mock_service

        request_data = {
            "description": "Test Description",
            "xrayType": "monitor",
            "prompt": "Test Prompt",
            "title": "Test Title",
            "emoji": "🔍",
            "shortSummary": "Test Summary",
        }

        headers = {"x-user-id": test_user_id} if include_user_id else {}

        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.post(
                "/v1.0/x-ray/xrays/create/step3",
                json=request_data,
                headers=headers,
            )

            return response

    # DELETE XRAY API TESTS

    @pytest.mark.asyncio
    async def test_delete_xray_success(self, app, test_user_id, mock_service):
        """Test successful X-Ray deletion via API"""
        mock_service.delete_xray.return_value = True

        app.dependency_overrides[get_xray_service] = lambda: mock_service

        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.delete(
                "/v1.0/x-ray/xrays/1",
                headers={"x-user-id": test_user_id},
            )

            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert data["message"] == "X-Ray deleted successfully"

        # Verify service was called with correct parameters
        mock_service.delete_xray.assert_called_once_with(
            xray_id=1, user_id=int(test_user_id)
        )

    @pytest.mark.asyncio
    async def test_delete_xray_not_found(self, app, test_user_id, mock_service):
        """Test delete returns 404 when X-Ray not found"""
        mock_service.delete_xray.return_value = False

        app.dependency_overrides[get_xray_service] = lambda: mock_service

        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.delete(
                "/v1.0/x-ray/xrays/999",
                headers={"x-user-id": test_user_id},
            )

            assert response.status_code == 404
            data = response.json()
            assert data["detail"] == "X-Ray not found"

    @pytest.mark.asyncio
    async def test_delete_xray_unauthorized(self, app):
        """Test delete without user ID header"""
        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.delete("/v1.0/x-ray/xrays/1")

            assert response.status_code == 422
            data = response.json()
            assert isinstance(data["detail"], list)
            assert any(
                err["loc"] == ["header", "x-user-id"] and err["msg"] == "Field required"
                for err in data["detail"]
            )

    @pytest.mark.asyncio
    async def test_delete_xray_invalid_id(self, app, test_user_id):
        """Test delete with invalid X-Ray ID format"""
        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.delete(
                "/v1.0/x-ray/xrays/invalid_id",
                headers={"x-user-id": test_user_id},
            )

            assert response.status_code == 422
            data = response.json()
            assert isinstance(data["detail"], list)
            # Should be a validation error for the path parameter

    @pytest.mark.asyncio
    async def test_delete_xray_service_error(self, app, test_user_id, mock_service):
        """Test delete handles service errors"""
        mock_service.delete_xray.side_effect = Exception("Database error")

        app.dependency_overrides[get_xray_service] = lambda: mock_service

        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.delete(
                "/v1.0/x-ray/xrays/1",
                headers={"x-user-id": test_user_id},
            )

            assert response.status_code == 500
            data = response.json()
            assert data["detail"] == "Internal server error"

    # GET XRAY TEMPLATE API TESTS

    @pytest.mark.asyncio
    async def test_get_xray_template_success(self, app, test_user_id, mock_service):
        """Test successful template retrieval via API"""
        # Create a simple object that can work with Pydantic serialization
        from types import SimpleNamespace

        mock_template = SimpleNamespace()
        mock_template.id = 1
        mock_template.ownerId = None  # camelCase for Pydantic
        mock_template.title = "Test Template"
        mock_template.description = "Test Description"
        mock_template.prompt = "Test Prompt"
        mock_template.icon = "📊"
        mock_template.shortSummary = "Test Summary"  # camelCase for Pydantic
        mock_template.xrayType = "monitor"  # camelCase for Pydantic
        mock_template.createdAt = datetime.now()  # camelCase for Pydantic
        mock_template.updatedAt = datetime.now()  # camelCase for Pydantic

        mock_service.get_xray_template_by_id.return_value = mock_template

        app.dependency_overrides[get_xray_service] = lambda: mock_service

        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.get(
                "/v1.0/x-ray/xray-templates/1",
                headers={"x-user-id": test_user_id},
            )

            assert response.status_code == 200
            data = response.json()
            assert data["id"] == 1
            assert data["ownerId"] is None
            assert data["title"] == "Test Template"
            assert data["description"] == "Test Description"
            assert data["prompt"] == "Test Prompt"
            assert data["icon"] == "📊"
            assert data["shortSummary"] == "Test Summary"
            assert data["xrayType"] == "monitor"
            assert "createdAt" in data
            assert "updatedAt" in data

        # Verify service was called with correct parameters
        mock_service.get_xray_template_by_id.assert_called_once_with(template_id=1)

    @pytest.mark.asyncio
    async def test_get_xray_template_not_found(self, app, test_user_id, mock_service):
        """Test get template when template doesn't exist"""
        mock_service.get_xray_template_by_id.return_value = None

        app.dependency_overrides[get_xray_service] = lambda: mock_service

        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.get(
                "/v1.0/x-ray/xray-templates/999",
                headers={"x-user-id": test_user_id},
            )

            assert response.status_code == 404
            data = response.json()
            assert data["detail"] == "Template not found"

    @pytest.mark.asyncio
    async def test_get_xray_template_unauthorized(self, app):
        """Test get without user ID header"""
        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.get("/v1.0/x-ray/xray-templates/1")

            assert response.status_code == 422
            data = response.json()
            assert isinstance(data["detail"], list)
            assert any(
                err["loc"] == ["header", "x-user-id"] and err["msg"] == "Field required"
                for err in data["detail"]
            )

    @pytest.mark.asyncio
    async def test_get_xray_template_invalid_id(self, app, test_user_id):
        """Test get with invalid template ID format"""
        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.get(
                "/v1.0/x-ray/xray-templates/invalid_id",
                headers={"x-user-id": test_user_id},
            )

            assert response.status_code == 422
            data = response.json()
            assert isinstance(data["detail"], list)
            # Should be a validation error for the path parameter

    @pytest.mark.asyncio
    async def test_get_xray_template_service_error(
        self, app, test_user_id, mock_service
    ):
        """Test get template handles service errors"""
        mock_service.get_xray_template_by_id.side_effect = Exception("Database error")

        app.dependency_overrides[get_xray_service] = lambda: mock_service

        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.get(
                "/v1.0/x-ray/xray-templates/1",
                headers={"x-user-id": test_user_id},
            )

            assert response.status_code == 500
            data = response.json()
            assert data["detail"] == "Internal server error"

    # SHARE XRAY AS TEMPLATE API TESTS

    @pytest.mark.asyncio
    async def test_share_xray_as_template_success(
        self, app, test_user_id, mock_service
    ):
        """Test successful X-Ray sharing as template via API"""
        # Create a template object that can work with Pydantic serialization
        from types import SimpleNamespace

        mock_template = SimpleNamespace()
        mock_template.id = 1
        mock_template.ownerId = 12345
        mock_template.title = "Shared Template"
        mock_template.description = "Shared Description"
        mock_template.prompt = "Shared Prompt"
        mock_template.icon = "📊"
        mock_template.shortSummary = "Shared Summary"
        mock_template.xrayType = "build"
        mock_template.createdAt = datetime.now()
        mock_template.updatedAt = datetime.now()

        mock_service.share_xray_as_template.return_value = mock_template

        app.dependency_overrides[get_xray_service] = lambda: mock_service

        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.post(
                "/v1.0/x-ray/xrays/1/share",
                headers={"x-user-id": test_user_id},
            )

            assert response.status_code == 200
            data = response.json()
            assert data["id"] == 1
            assert data["ownerId"] == 12345
            assert data["title"] == "Shared Template"

        mock_service.share_xray_as_template.assert_called_once_with(
            xray_id=1, user_id=12345
        )

    @pytest.mark.asyncio
    async def test_share_xray_as_template_xray_not_found(
        self, app, test_user_id, mock_service
    ):
        """Test share when X-Ray doesn't exist"""
        mock_service.share_xray_as_template.return_value = None

        app.dependency_overrides[get_xray_service] = lambda: mock_service

        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.post(
                "/v1.0/x-ray/xrays/999/share",
                headers={"x-user-id": test_user_id},
            )

            assert response.status_code == 404
            data = response.json()
            assert data["detail"] == "X-Ray not found"

    @pytest.mark.asyncio
    async def test_share_xray_as_template_no_auth(self, app, mock_service):
        """Test that authentication is required"""
        app.dependency_overrides[get_xray_service] = lambda: mock_service

        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.post("/v1.0/x-ray/xrays/1/share")

            # FastAPI returns 422 when required dependencies fail
            assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_share_xray_as_template_service_error(
        self, app, test_user_id, mock_service
    ):
        """Test share handles service errors"""
        mock_service.share_xray_as_template.side_effect = Exception("Database error")

        app.dependency_overrides[get_xray_service] = lambda: mock_service

        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.post(
                "/v1.0/x-ray/xrays/1/share",
                headers={"x-user-id": test_user_id},
            )

            assert response.status_code == 500
            data = response.json()
            assert data["detail"] == "Internal server error"

    @pytest.mark.asyncio
    async def test_share_xray_as_template_idempotent(
        self, app, test_user_id, mock_service
    ):
        """Test that sharing returns same template if already exists (idempotent)"""
        # Create a template object that can work with Pydantic serialization
        from types import SimpleNamespace

        mock_template = SimpleNamespace()
        mock_template.id = 2  # Different ID to show existing template returned
        mock_template.ownerId = 12345
        mock_template.title = "Existing Template"
        mock_template.description = "Existing Description"
        mock_template.prompt = "Existing Prompt"
        mock_template.icon = "📋"
        mock_template.shortSummary = "Existing Summary"
        mock_template.xrayType = "monitor"
        mock_template.createdAt = datetime.now()
        mock_template.updatedAt = datetime.now()

        mock_service.share_xray_as_template.return_value = mock_template

        app.dependency_overrides[get_xray_service] = lambda: mock_service

        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            # First call
            response1 = await client.post(
                "/v1.0/x-ray/xrays/1/share",
                headers={"x-user-id": test_user_id},
            )

            # Second call (should return same result)
            response2 = await client.post(
                "/v1.0/x-ray/xrays/1/share",
                headers={"x-user-id": test_user_id},
            )

            assert response1.status_code == 200
            assert response2.status_code == 200

            data1 = response1.json()
            data2 = response2.json()

            # Should return same template ID (idempotent behavior)
            assert data1["id"] == data2["id"] == 2
            assert data1["title"] == data2["title"] == "Existing Template"

        # Service should be called twice (once for each API call)
        assert mock_service.share_xray_as_template.call_count == 2
