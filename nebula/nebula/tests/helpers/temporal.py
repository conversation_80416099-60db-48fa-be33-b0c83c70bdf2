from contextlib import asynccontextmanager

from nebula.temporal.workflows.ai_end_meeting_wf import AiEndMeetingWorkflow
from temporalio.worker import Worker
from temporalio.client import Client

from nebula.temporal.workflows.metadata_wf import MetadataWorkflow
from nebula.temporal.workflows.metadata_backfill_wf import BackfillMetadataWorkflow
from nebula.temporal.workflows.post_session_summary_wf import PostSessionSummaryWorkflow
from nebula.temporal.workflows.typesense_indexing_wf import TypesenseIndexingWorkflow
from nebula.temporal.workflows.tldr_generation_wf import TLDRGenerationWorkflow

from nebula.temporal.workflows.meeting_suggestions_wf import MeetingSuggestionsWorkflow
from nebula.temporal.workflows.xray_wf import XRayScanWorkflow
from nebula.tests.mocks.mock_metadata_activities import MockMetadataActivities
from nebula.tests.mocks.mock_recurrence_activities import MockRecurrenceActivities
from nebula.tests.mocks.mock_post_session_summary_activities import (
    MockPostSessionSummaryActivities,
)
from nebula.tests.mocks.mock_typesense_activities import MockTypesenseActivities
from nebula.tests.mocks.mock_tldr_activities import MockTLDRActivities

from nebula.tests.mocks.mock_meeting_suggestions_activities import (
    MockMeetingSuggestionsActivities,
)
from nebula.tests.mocks.mock_shared_activities import MockSharedActivities
from nebula.tests.mocks.mock_xray_activities import MockXRayActivities


@asynccontextmanager
async def create_ai_end_meet_worker_for_tests(
    client: Client,
    task_queue: str,
):
    """Creates a Temporal worker with all required workflows and activities for the ai end meeting worker.

    This matches the production worker configuration (Runs actual temporal server).
    Args:
        client: The Temporal client to use.
        task_queue: The task queue to listen on.

    Yields:
        Worker: A running Worker instance configured with all workflows and mocked activities.
    """

    # Include both workflow types as in production

    workflows = [
        MetadataWorkflow,
        BackfillMetadataWorkflow,
        AiEndMeetingWorkflow,
        PostSessionSummaryWorkflow,
        TypesenseIndexingWorkflow,
        TLDRGenerationWorkflow,
        MeetingSuggestionsWorkflow,
        XRayScanWorkflow,
    ]

    # Setup mock activities
    mock_shared_activities = MockSharedActivities()
    mock_metadata_activities = MockMetadataActivities()
    mock_recurrence_activities = MockRecurrenceActivities()
    mock_post_session_summary_activities = MockPostSessionSummaryActivities()
    mock_typesense_activities = MockTypesenseActivities()
    mock_tldr_activities = MockTLDRActivities()
    mock_meeting_suggestions_activities = MockMeetingSuggestionsActivities()
    mock_xray_activities = MockXRayActivities()

    # Register all activities
    activities = [
        # Shared activities (used by multiple workflows)
        mock_shared_activities.get_transcription_batches,
        mock_shared_activities.get_session_data,
        mock_shared_activities.process_transcript,
        # Metadata-specific activities
        mock_metadata_activities.create_metadata_record,
        mock_metadata_activities.extract_metadata,
        mock_metadata_activities.get_metadata_record,
        mock_metadata_activities.update_elio_about_metadata_creation,
        # Recurrence activities
        mock_recurrence_activities.get_past_ai_enabled_recurrences,
        # Post session summary activities
        mock_post_session_summary_activities.get_transcription_batches_for_pss,
        mock_post_session_summary_activities.get_session_data_for_pss,
        mock_post_session_summary_activities.process_transcript_for_pss,
        mock_post_session_summary_activities.create_or_update_post_session_summary,
        mock_post_session_summary_activities.generate_post_meeting_summary,
        mock_post_session_summary_activities.get_action_items_for_pss,
        mock_post_session_summary_activities.notify_draconids_summary_ready,
        # Typesense activities
        mock_typesense_activities.get_recurrence_data,
        mock_typesense_activities.process_transcripts_for_typesense,
        mock_typesense_activities.index_meeting_in_typesense,
        # TLDR activities
        mock_tldr_activities.generate_tldr_summary,
        mock_tldr_activities.update_post_session_summary_with_tldr,
        mock_tldr_activities.notify_luxor_tldr_ready,
        # Meeting suggestions specific activities
        mock_meeting_suggestions_activities.get_all_participants,
        mock_meeting_suggestions_activities.create_all_users_meeting_suggestions_record,
        mock_meeting_suggestions_activities.process_transcript_with_user_ids,
        mock_meeting_suggestions_activities.extract_all_users_meeting_suggestions,
        # X-Ray activities
        mock_xray_activities.get_xrays,
        mock_xray_activities.get_transcript_by_recurrence_id,
        mock_xray_activities.quick_scan,
        mock_xray_activities.get_xray_by_id,
        mock_xray_activities.deep_scan,
        # X-Ray backfill activities
        mock_xray_activities.get_user_by_id,
        mock_xray_activities.get_past_user_session_recurrences_by_user_id,
    ]

    # Create and start the worker
    async with Worker(
        client,
        task_queue=task_queue,
        workflows=workflows,
        activities=activities,
    ) as worker:
        yield worker
