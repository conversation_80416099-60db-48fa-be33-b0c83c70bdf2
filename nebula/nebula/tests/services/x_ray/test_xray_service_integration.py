import pytest
import pytest_asyncio
import asyncio
import sys
from datetime import datetime, timedelta

from nebula.services.x_ray.xray_service import (
    XRayService,
    XRayTypeFilter,
    XRaySortBy,
    XRayUpdateData,
)
from nebula.db.models.xray import XRay
from nebula.db.models.xray_template import XRayTemplate


@pytest.mark.integration
class TestXRayServiceIntegration:
    """Integration tests for XRayService with real database operations"""

    @pytest.fixture
    def service(self, nebula_db_pool):
        """Create XRayService instance with test database"""
        # nebula_db_pool ensures the test database exists
        return XRayService()

    @pytest.fixture
    def test_user_id(self):
        """Standard test user ID"""
        return 12345

    @pytest.fixture
    def another_user_id(self):
        """Another user ID for testing isolation"""
        return 67890

    @pytest.fixture
    def sample_xray_data(self, test_user_id):
        """Standard X-Ray test data"""
        return {
            "user_id": test_user_id,
            "description": "Track team progress and blockers from weekly meetings",
            "xray_type": "monitor",
            "prompt": "Extract status updates, blockers, and action items",
            "title": "Weekly Status Monitor",
            "emoji": "📊",
            "short_summary": "Monitor weekly team status updates and identify blockers",
        }

    @pytest_asyncio.fixture
    async def sample_xray(self, test_user_id):
        """Create a sample X-Ray for testing"""
        xray_data = await XRay.insert(
            XRay(
                owner_id=test_user_id,
                title="Test X-Ray for Sharing",
                description="Test description for sharing",
                prompt="Test prompt for meeting analysis",
                icon="📊",
                short_summary="Test summary for sharing",
                xray_type="build",
                visibility="user",
                scope="all",
                alert_channels={"notifyAuthorEmail": True},
                is_active=True,
            )
        )

        # Return the created X-Ray object
        xray = await XRay.objects().where(XRay.id == xray_data[0]["id"]).first()
        yield xray

        # Cleanup is handled by the cleanup_xrays fixture

    @pytest_asyncio.fixture(autouse=True)
    async def cleanup_xrays(self, nebula_db_pool):
        """Clean up X-Rays before and after each test"""
        print(f"\nDEBUG: cleanup_xrays fixture called!", file=sys.stderr)
        # Clean up before test
        try:
            # Delete all X-Rays
            deleted_count = await XRay.delete(force=True)
            print(
                f"DEBUG: Deleted {deleted_count} X-Rays before test",
                file=sys.stderr,
            )

            # Delete all X-Ray templates
            template_count = await XRayTemplate.delete(force=True)
            print(
                f"DEBUG: Deleted {template_count} X-Ray templates before test",
                file=sys.stderr,
            )
        except Exception as e:
            print(f"DEBUG: Error during cleanup: {e}", file=sys.stderr)

        yield

        # Clean up after test
        try:
            deleted_count = await XRay.delete(force=True)
            template_count = await XRayTemplate.delete(force=True)
            print(
                f"DEBUG: Cleaned up {deleted_count} X-Rays and {template_count} templates after test",
                file=sys.stderr,
            )
        except Exception as e:
            print(f"DEBUG: Error during cleanup after test: {e}", file=sys.stderr)

    # CREATE XRAY TESTS

    @pytest.mark.asyncio
    async def test_create_xray_creates_valid_record(
        self, service, nebula_db_pool, sample_xray_data
    ):
        """Test successful X-Ray creation with all fields"""
        print(f"\nTEST RUNNING: nebula_db_pool = {nebula_db_pool}", file=sys.stderr)
        print(f"TEST RUNNING: sample_xray_data = {sample_xray_data}", file=sys.stderr)
        xray = await service.create_xray(**sample_xray_data)

        assert xray.id is not None
        assert xray.owner_id == sample_xray_data["user_id"]
        assert xray.title == sample_xray_data["title"]
        assert xray.description == sample_xray_data["description"]
        assert xray.xray_type == sample_xray_data["xray_type"]
        assert xray.prompt == sample_xray_data["prompt"]
        assert xray.icon == sample_xray_data["emoji"]
        assert xray.short_summary == sample_xray_data["short_summary"]
        assert xray.alert_channels == {}
        assert xray.is_active is True
        assert xray.visibility == "user"
        assert xray.scope == "all"
        assert xray.created_at is not None
        assert xray.updated_at is not None

        db_xray = await XRay.objects().where(XRay.id == xray.id).first()
        assert db_xray is not None
        assert db_xray.title == sample_xray_data["title"]

    @pytest.mark.asyncio
    async def test_create_xray_with_empty_description_raises_error(
        self, service, nebula_db_pool, test_user_id
    ):
        """Test X-Ray creation fails with empty description"""
        with pytest.raises(ValueError, match="Description cannot be empty"):
            await service.create_xray(
                user_id=test_user_id,
                description="",
                xray_type="monitor",
                prompt="Test prompt",
                title="Test Title",
                emoji="📊",
                short_summary="Test summary",
            )

    @pytest.mark.asyncio
    async def test_create_xray_with_empty_title_raises_error(
        self, service, nebula_db_pool, test_user_id
    ):
        """Test X-Ray creation fails with empty title"""
        with pytest.raises(ValueError, match="Title cannot be empty"):
            await service.create_xray(
                user_id=test_user_id,
                description="Test description",
                xray_type="monitor",
                prompt="Test prompt",
                title="",
                emoji="📊",
                short_summary="Test summary",
            )

    @pytest.mark.asyncio
    async def test_create_xray_with_whitespace_only_fields_raises_error(
        self, service, nebula_db_pool, test_user_id
    ):
        """Test X-Ray creation fails with whitespace-only fields"""
        with pytest.raises(ValueError, match="Prompt cannot be empty"):
            await service.create_xray(
                user_id=test_user_id,
                description="Test description",
                xray_type="monitor",
                prompt="   ",
                title="Test Title",
                emoji="📊",
                short_summary="Test summary",
            )

    # GET XRAYS PAGINATED TESTS

    @pytest.mark.asyncio
    async def test_get_xrays_paginated_returns_user_xrays_only(
        self, service, nebula_db_pool, test_user_id, another_user_id
    ):
        """Test that pagination returns only X-Rays owned by the requesting user"""
        # Debug: Check what X-Rays exist before the test
        existing_xrays = await XRay.objects()
        print(
            f"\nDEBUG: Existing X-Rays before test: {len(existing_xrays)}",
            file=sys.stderr,
        )
        for xray in existing_xrays:
            print(f"  - {xray.title} (owner_id={xray.owner_id})", file=sys.stderr)

        await XRay.insert(
            XRay(
                owner_id=test_user_id,
                title="User's X-Ray",
                description="Test",
                prompt="Test prompt",
                icon="📊",
                short_summary="Test summary",
                xray_type="monitor",
                alert_channels={},
                is_active=True,
                visibility="user",
                scope="all",
            ),
            XRay(
                owner_id=another_user_id,
                title="Other User's X-Ray",
                description="Test",
                prompt="Test prompt",
                icon="📊",
                short_summary="Test summary",
                xray_type="monitor",
                alert_channels={},
                is_active=True,
                visibility="user",
                scope="all",
            ),
        )

        xrays, total, has_more = await service.get_xrays_paginated(
            user_id=test_user_id, limit=10, skip=0
        )

        assert total == 1
        assert len(xrays) == 1
        assert xrays[0].owner_id == test_user_id
        assert xrays[0].title == "User's X-Ray"

    @pytest.mark.asyncio
    async def test_get_xrays_paginated_with_type_filter_monitor(
        self, service, nebula_db_pool, test_user_id
    ):
        """Test filtering X-Rays by type 'monitor'"""
        await XRay.insert(
            XRay(
                owner_id=test_user_id,
                title="Monitor 1",
                xray_type="monitor",
                description="Test",
                prompt="Test prompt",
                icon="📊",
                short_summary="Test summary",
                alert_channels={},
                is_active=True,
                visibility="user",
                scope="all",
            ),
            XRay(
                owner_id=test_user_id,
                title="Build 1",
                xray_type="build",
                description="Test",
                prompt="Test prompt",
                icon="📊",
                short_summary="Test summary",
                alert_channels={},
                is_active=True,
                visibility="user",
                scope="all",
            ),
            XRay(
                owner_id=test_user_id,
                title="Monitor 2",
                xray_type="monitor",
                description="Test",
                prompt="Test prompt",
                icon="📊",
                short_summary="Test summary",
                alert_channels={},
                is_active=True,
                visibility="user",
                scope="all",
            ),
            XRay(
                owner_id=test_user_id,
                title="Digest 1",
                xray_type="digest",
                description="Test",
                prompt="Test prompt",
                icon="📊",
                short_summary="Test summary",
                alert_channels={},
                is_active=True,
                visibility="user",
                scope="all",
            ),
        )

        xrays, total, has_more = await service.get_xrays_paginated(
            user_id=test_user_id, xray_type=XRayTypeFilter.MONITOR, limit=10, skip=0
        )

        assert total == 2
        assert len(xrays) == 2
        assert all(xray.xray_type == "monitor" for xray in xrays)
        assert {xray.title for xray in xrays} == {"Monitor 1", "Monitor 2"}

    @pytest.mark.asyncio
    async def test_get_xrays_paginated_sorted_by_last_updated(
        self, service, nebula_db_pool, test_user_id
    ):
        """Test X-Rays are sorted by last updated timestamp descending"""
        now = datetime.utcnow()

        xray1 = XRay(
            owner_id=test_user_id,
            title="Old X-Ray",
            description="Test",
            prompt="Test prompt",
            icon="📊",
            short_summary="Test summary",
            xray_type="monitor",
            alert_channels={},
            is_active=True,
            visibility="user",
            scope="all",
            updated_at=now - timedelta(days=7),
        )
        xray2 = XRay(
            owner_id=test_user_id,
            title="Recent X-Ray",
            description="Test",
            prompt="Test prompt",
            icon="📊",
            short_summary="Test summary",
            xray_type="monitor",
            alert_channels={},
            is_active=True,
            visibility="user",
            scope="all",
            updated_at=now - timedelta(days=1),
        )
        xray3 = XRay(
            owner_id=test_user_id,
            title="Latest X-Ray",
            description="Test",
            prompt="Test prompt",
            icon="📊",
            short_summary="Test summary",
            xray_type="monitor",
            alert_channels={},
            is_active=True,
            visibility="user",
            scope="all",
            updated_at=now,
        )

        await XRay.insert(xray1, xray2, xray3)

        xrays, _, _ = await service.get_xrays_paginated(
            user_id=test_user_id, sort_by=XRaySortBy.LAST_UPDATED, limit=10, skip=0
        )

        assert xrays[0].title == "Latest X-Ray"
        assert xrays[1].title == "Recent X-Ray"
        assert xrays[2].title == "Old X-Ray"

    @pytest.mark.asyncio
    async def test_get_xrays_paginated_sorted_alphabetically(
        self, service, nebula_db_pool, test_user_id
    ):
        """Test X-Rays are sorted alphabetically by title"""
        await XRay.insert(
            XRay(
                owner_id=test_user_id,
                title="Zebra X-Ray",
                description="Test",
                prompt="Test prompt",
                icon="📊",
                short_summary="Test summary",
                xray_type="monitor",
                alert_channels={},
                is_active=True,
                visibility="user",
                scope="all",
            ),
            XRay(
                owner_id=test_user_id,
                title="Alpha X-Ray",
                description="Test",
                prompt="Test prompt",
                icon="📊",
                short_summary="Test summary",
                xray_type="monitor",
                alert_channels={},
                is_active=True,
                visibility="user",
                scope="all",
            ),
            XRay(
                owner_id=test_user_id,
                title="Beta X-Ray",
                description="Test",
                prompt="Test prompt",
                icon="📊",
                short_summary="Test summary",
                xray_type="monitor",
                alert_channels={},
                is_active=True,
                visibility="user",
                scope="all",
            ),
        )

        xrays, _, _ = await service.get_xrays_paginated(
            user_id=test_user_id, sort_by=XRaySortBy.ALPHABETICAL, limit=10, skip=0
        )

        assert xrays[0].title == "Alpha X-Ray"
        assert xrays[1].title == "Beta X-Ray"
        assert xrays[2].title == "Zebra X-Ray"

    @pytest.mark.asyncio
    async def test_get_xrays_paginated_pagination_limits(
        self, service, nebula_db_pool, test_user_id
    ):
        """Test pagination with different limit and skip values"""
        for i in range(15):
            await XRay.insert(
                XRay(
                    owner_id=test_user_id,
                    title=f"X-Ray {i:02d}",
                    description="Test",
                    prompt="Test prompt",
                    icon="📊",
                    short_summary="Test summary",
                    xray_type="monitor",
                    alert_channels={},
                    is_active=True,
                    visibility="user",
                    scope="all",
                )
            )

        xrays, total, has_more = await service.get_xrays_paginated(
            user_id=test_user_id, limit=10, skip=0
        )
        assert len(xrays) == 10
        assert total == 15
        assert has_more is True

        xrays, total, has_more = await service.get_xrays_paginated(
            user_id=test_user_id, limit=10, skip=10
        )
        assert len(xrays) == 5
        assert total == 15
        assert has_more is False

        xrays, total, has_more = await service.get_xrays_paginated(
            user_id=test_user_id, limit=5, skip=5
        )
        assert len(xrays) == 5
        assert total == 15
        assert has_more is True

    @pytest.mark.asyncio
    async def test_get_xrays_paginated_empty_results(
        self, service, nebula_db_pool, test_user_id
    ):
        """Test pagination returns empty list when no X-Rays exist"""
        xrays, total, has_more = await service.get_xrays_paginated(
            user_id=test_user_id, limit=10, skip=0
        )

        assert xrays == []
        assert total == 0
        assert has_more is False

    # UPDATE XRAY TESTS

    @pytest.mark.asyncio
    async def test_update_xray_updates_only_provided_fields(
        self, service, nebula_db_pool, test_user_id, sample_xray_data
    ):
        """Test partial update only modifies provided fields"""
        xray = await service.create_xray(**sample_xray_data)
        original_description = xray.description
        original_updated_at = xray.updated_at

        await asyncio.sleep(0.1)

        update_data = XRayUpdateData(
            title="Updated Title",
            alert_channels={"notifyAuthorEmail": True},
        )

        updated_xray = await service.update_xray(
            xray_id=xray.id, user_id=test_user_id, update_data=update_data
        )

        assert updated_xray is not None
        assert updated_xray.title == "Updated Title"
        assert updated_xray.alert_channels == {"notifyAuthorEmail": True}
        assert updated_xray.description == original_description
        assert updated_xray.updated_at > original_updated_at

        db_xray = await XRay.objects().where(XRay.id == xray.id).first()
        assert db_xray.title == "Updated Title"
        # JSONB fields are returned as strings when using .objects(), so we need to parse them
        import json

        if isinstance(db_xray.alert_channels, str):
            alert_channels = json.loads(db_xray.alert_channels)
        else:
            alert_channels = db_xray.alert_channels
        assert alert_channels == {"notifyAuthorEmail": True}

    @pytest.mark.asyncio
    async def test_update_xray_with_is_active_field(
        self, service, nebula_db_pool, test_user_id, sample_xray_data
    ):
        """Test updating is_active field"""
        xray = await service.create_xray(**sample_xray_data)
        assert xray.is_active is True

        update_data = XRayUpdateData(is_active=False)

        updated_xray = await service.update_xray(
            xray_id=xray.id, user_id=test_user_id, update_data=update_data
        )

        assert updated_xray is not None
        assert updated_xray.is_active is False

        db_xray = await XRay.objects().where(XRay.id == xray.id).first()
        assert db_xray.is_active is False

    @pytest.mark.asyncio
    async def test_update_xray_with_wrong_user_id_returns_none(
        self, service, nebula_db_pool, test_user_id, another_user_id, sample_xray_data
    ):
        """Test update fails when user doesn't own the X-Ray"""
        xray = await service.create_xray(**sample_xray_data)

        result = await service.update_xray(
            xray_id=xray.id,
            user_id=another_user_id,
            update_data=XRayUpdateData(title="Hacked Title"),
        )

        assert result is None

        db_xray = await XRay.objects().where(XRay.id == xray.id).first()
        assert db_xray.title == sample_xray_data["title"]

    @pytest.mark.asyncio
    async def test_update_xray_with_nonexistent_id_returns_none(
        self, service, nebula_db_pool, test_user_id
    ):
        """Test update returns None for non-existent X-Ray"""
        result = await service.update_xray(
            xray_id=99999,
            user_id=test_user_id,
            update_data=XRayUpdateData(title="New Title"),
        )

        assert result is None

    # GET XRAY BY ID TESTS

    @pytest.mark.asyncio
    async def test_get_xray_by_id_returns_correct_xray(
        self, service, nebula_db_pool, test_user_id, sample_xray_data
    ):
        """Test retrieving X-Ray by ID"""
        xray = await service.create_xray(**sample_xray_data)

        retrieved_xray = await service.get_xray_by_id(
            xray_id=xray.id, user_id=test_user_id
        )

        assert retrieved_xray is not None
        assert retrieved_xray.id == xray.id
        assert retrieved_xray.title == xray.title

    @pytest.mark.asyncio
    async def test_get_xray_by_id_with_wrong_user_returns_none(
        self, service, nebula_db_pool, test_user_id, another_user_id, sample_xray_data
    ):
        """Test get_xray_by_id returns None when user doesn't own the X-Ray"""
        xray = await service.create_xray(**sample_xray_data)

        result = await service.get_xray_by_id(xray_id=xray.id, user_id=another_user_id)

        assert result is None

    @pytest.mark.asyncio
    async def test_get_xray_by_id_with_nonexistent_id_returns_none(
        self, service, nebula_db_pool, test_user_id
    ):
        """Test get_xray_by_id returns None for non-existent X-Ray"""
        result = await service.get_xray_by_id(xray_id=99999, user_id=test_user_id)

        assert result is None

    # GET XRAY TEMPLATES PAGINATED TESTS

    @pytest.mark.asyncio
    async def test_get_xray_templates_paginated_returns_all_templates(
        self, service, nebula_db_pool
    ):
        """Test retrieving paginated X-Ray templates"""
        await XRayTemplate.insert(
            XRayTemplate(
                owner_id=None,
                title="Template 1",
                description="Test template 1",
                prompt="Template prompt 1",
                icon="📊",
                short_summary="Template summary 1",
                xray_type="monitor",
            ),
            XRayTemplate(
                owner_id=None,
                title="Template 2",
                description="Test template 2",
                prompt="Template prompt 2",
                icon="📈",
                short_summary="Template summary 2",
                xray_type="build",
            ),
        )

        templates, total, has_more = await service.get_xray_templates_paginated(
            limit=10, skip=0
        )

        assert len(templates) == 2
        assert total == 2
        assert has_more is False
        assert {t.title for t in templates} == {"Template 1", "Template 2"}

    @pytest.mark.asyncio
    async def test_get_xray_templates_paginated_with_pagination(
        self, service, nebula_db_pool
    ):
        """Test template pagination with limit and skip"""
        for i in range(5):
            await XRayTemplate.insert(
                XRayTemplate(
                    owner_id=None,
                    title=f"Template {i}",
                    description=f"Test template {i}",
                    prompt=f"Template prompt {i}",
                    icon="📊",
                    short_summary=f"Template summary {i}",
                    xray_type="monitor",
                )
            )

        templates, total, has_more = await service.get_xray_templates_paginated(
            limit=3, skip=0
        )
        assert len(templates) == 3
        assert total == 5
        assert has_more is True

        templates, total, has_more = await service.get_xray_templates_paginated(
            limit=3, skip=3
        )
        assert len(templates) == 2
        assert total == 5
        assert has_more is False

    @pytest.mark.asyncio
    async def test_get_xray_templates_paginated_filters_out_user_templates(
        self, service, nebula_db_pool, test_user_id
    ):
        """Test that user-created templates are filtered out and only Rumi templates are returned"""
        # Create a mix of Rumi templates (owner_id=None) and user templates (owner_id=user_id)
        await XRayTemplate.insert(
            # Rumi template 1
            XRayTemplate(
                owner_id=None,
                title="Rumi Template 1",
                description="Rumi template 1",
                prompt="Rumi prompt 1",
                icon="📊",
                short_summary="Rumi summary 1",
                xray_type="monitor",
            ),
            # User template (should be filtered out)
            XRayTemplate(
                owner_id=test_user_id,
                title="User Template 1",
                description="User template 1",
                prompt="User prompt 1",
                icon="👤",
                short_summary="User summary 1",
                xray_type="build",
            ),
            # Rumi template 2
            XRayTemplate(
                owner_id=None,
                title="Rumi Template 2",
                description="Rumi template 2",
                prompt="Rumi prompt 2",
                icon="📈",
                short_summary="Rumi summary 2",
                xray_type="build",
            ),
            # Another user's template (should be filtered out)
            XRayTemplate(
                owner_id=test_user_id + 1,
                title="Other User Template",
                description="Other user template",
                prompt="Other user prompt",
                icon="🔄",
                short_summary="Other user summary",
                xray_type="monitor",
            ),
        )

        # Request templates - should only get Rumi templates (owner_id=None)
        templates, total, has_more = await service.get_xray_templates_paginated(
            limit=10, skip=0
        )

        # Should only return the 2 Rumi templates, filtering out user-created ones
        assert len(templates) == 2
        assert total == 2
        assert has_more is False
        
        # Verify only Rumi templates are returned
        template_titles = {t.title for t in templates}
        assert template_titles == {"Rumi Template 1", "Rumi Template 2"}
        
        # Verify all returned templates have owner_id=None
        for template in templates:
            assert template.owner_id is None

    # CONCURRENT OPERATIONS TESTS

    @pytest.mark.asyncio
    async def test_concurrent_xray_creation_succeeds(
        self, service, nebula_db_pool, test_user_id
    ):
        """Test concurrent X-Ray creation doesn't cause conflicts"""

        async def create_xray(index):
            return await service.create_xray(
                user_id=test_user_id,
                description=f"Concurrent test {index}",
                xray_type="monitor",
                prompt=f"Test prompt {index}",
                title=f"Concurrent X-Ray {index}",
                emoji="🔍",
                short_summary=f"Test summary {index}",
            )

        tasks = [create_xray(i) for i in range(10)]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        assert all(not isinstance(r, Exception) for r in results)
        assert len(results) == 10
        assert all(r.id is not None for r in results)

        count = await XRay.count().where(XRay.owner_id == test_user_id)
        assert count == 10

    @pytest.mark.asyncio
    async def test_concurrent_updates_to_same_xray_last_write_wins(
        self, service, nebula_db_pool, test_user_id, sample_xray_data
    ):
        """Test that concurrent updates to same X-Ray don't cause deadlock"""
        xray = await service.create_xray(**sample_xray_data)

        async def update_1():
            return await service.update_xray(
                xray_id=xray.id,
                user_id=test_user_id,
                update_data=XRayUpdateData(title="Update 1", is_active=False),
            )

        async def update_2():
            return await service.update_xray(
                xray_id=xray.id,
                user_id=test_user_id,
                update_data=XRayUpdateData(
                    title="Update 2", alert_channels={"notifyAuthorEmail": True}
                ),
            )

        results = await asyncio.gather(update_1(), update_2(), return_exceptions=True)

        assert all(not isinstance(r, Exception) for r in results)
        assert all(r is not None for r in results)

        final_xray = await XRay.objects().where(XRay.id == xray.id).first()
        assert final_xray.title in ["Update 1", "Update 2"]

    @pytest.mark.asyncio
    async def test_concurrent_reads_and_writes_succeed(
        self, service, nebula_db_pool, test_user_id, sample_xray_data
    ):
        """Test concurrent reads and writes don't interfere"""
        xray = await service.create_xray(**sample_xray_data)

        async def read_xray():
            return await service.get_xray_by_id(xray.id, test_user_id)

        async def update_xray():
            return await service.update_xray(
                xray_id=xray.id,
                user_id=test_user_id,
                update_data=XRayUpdateData(title="Updated During Read"),
            )

        async def list_xrays():
            return await service.get_xrays_paginated(user_id=test_user_id)

        tasks = [
            read_xray(),
            update_xray(),
            list_xrays(),
            read_xray(),
            update_xray(),
        ]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        assert all(not isinstance(r, Exception) for r in results)

    # DELETE XRAY INTEGRATION TESTS

    @pytest.mark.asyncio
    async def test_delete_xray_actually_removes_from_db(
        self, service, nebula_db_pool, test_user_id, sample_xray_data
    ):
        """Test that delete actually removes X-Ray from database"""
        # Create an X-Ray
        xray = await service.create_xray(**sample_xray_data)
        xray_id = xray.id

        # Verify it exists
        found_xray = await service.get_xray_by_id(xray_id=xray_id, user_id=test_user_id)
        assert found_xray is not None

        # Delete it
        deleted = await service.delete_xray(xray_id=xray_id, user_id=test_user_id)
        assert deleted is True

        # Verify it's gone
        found_xray = await service.get_xray_by_id(xray_id=xray_id, user_id=test_user_id)
        assert found_xray is None

        # Verify it's also gone from direct DB query
        db_xray = await XRay.objects().where(XRay.id == xray_id).first()
        assert db_xray is None

    @pytest.mark.asyncio
    async def test_delete_xray_cross_user_isolation(
        self, service, nebula_db_pool, test_user_id, another_user_id, sample_xray_data
    ):
        """Test users can't delete each other's X-Rays"""
        # Create X-Ray for user 1
        xray = await service.create_xray(**sample_xray_data)
        xray_id = xray.id

        # Try to delete as user 2
        deleted = await service.delete_xray(xray_id=xray_id, user_id=another_user_id)
        assert deleted is False

        # Verify it still exists for user 1
        found_xray = await service.get_xray_by_id(xray_id=xray_id, user_id=test_user_id)
        assert found_xray is not None

    @pytest.mark.asyncio
    async def test_delete_nonexistent_xray_returns_false(
        self, service, nebula_db_pool, test_user_id
    ):
        """Test delete returns False for non-existent X-Ray"""
        deleted = await service.delete_xray(xray_id=99999, user_id=test_user_id)
        assert deleted is False

    # GET XRAY TEMPLATE BY ID INTEGRATION TESTS

    @pytest.mark.asyncio
    async def test_get_xray_template_by_id_returns_rumi_template(
        self, service, nebula_db_pool
    ):
        """Test retrieving Rumi-generated template from database"""
        # Create a Rumi template (owner_id = null)
        templates = await XRayTemplate.insert(
            XRayTemplate(
                owner_id=None,  # Rumi-generated template
                title="Rumi Test Template",
                description="Test template description",
                prompt="Test template prompt",
                icon="📊",
                short_summary="Test template summary",
                xray_type="monitor",
            )
        )
        template = templates[0]  # Get the first (and only) inserted template

        # Retrieve the template
        template_id = template["id"]  # templates[0] is a dict with the inserted data
        found_template = await service.get_xray_template_by_id(template_id=template_id)

        assert found_template is not None
        assert found_template.id == template_id
        assert found_template.title == "Rumi Test Template"
        assert found_template.owner_id is None

    @pytest.mark.asyncio
    async def test_get_xray_template_by_id_ignores_user_templates(
        self, service, nebula_db_pool, test_user_id
    ):
        """Test that user-owned templates are not returned"""
        # Create a user template (owner_id = user_id)
        templates = await XRayTemplate.insert(
            XRayTemplate(
                owner_id=test_user_id,  # User-owned template
                title="User Test Template",
                description="User template description",
                prompt="User template prompt",
                icon="📊",
                short_summary="User template summary",
                xray_type="monitor",
            )
        )
        template = templates[0]  # Get the first (and only) inserted template

        # Try to retrieve the template (should not be found due to filtering)
        template_id = template["id"]  # templates[0] is a dict with the inserted data
        found_template = await service.get_xray_template_by_id(template_id=template_id)

        assert found_template is None

    @pytest.mark.asyncio
    async def test_get_xray_template_by_id_nonexistent_returns_none(
        self, service, nebula_db_pool
    ):
        """Test get returns None for non-existent template"""
        found_template = await service.get_xray_template_by_id(template_id=99999)
        assert found_template is None

    @pytest.mark.asyncio
    async def test_share_xray_as_template_integration_success(
        self, service, sample_xray, test_user_id
    ):
        """Integration test: Successfully share X-Ray as template"""

        try:
            # Share the X-Ray as a template
            result = await service.share_xray_as_template(
                xray_id=sample_xray.id, user_id=test_user_id
            )

            # Verify the result
            assert result is not None
            assert result.owner_id == test_user_id
            assert result.title == sample_xray.title
            assert result.description == sample_xray.description
            assert result.prompt == sample_xray.prompt
            assert result.icon == sample_xray.icon
            assert result.short_summary == sample_xray.short_summary
            assert result.xray_type == sample_xray.xray_type

            # Verify template was actually created in database
            db_template = (
                await XRayTemplate.objects().where(XRayTemplate.id == result.id).first()
            )
            assert db_template is not None
            assert db_template.owner_id == test_user_id
            assert db_template.title == sample_xray.title

        finally:
            # Cleanup - delete the template
            if result:
                await XRayTemplate.delete().where(XRayTemplate.id == result.id)

    @pytest.mark.asyncio
    async def test_share_xray_as_template_integration_duplicate_prevention(
        self, service, sample_xray, test_user_id
    ):
        """Integration test: Prevent duplicate templates (idempotent behavior)"""

        template1 = None
        template2 = None

        try:
            # First share - should create new template
            template1 = await service.share_xray_as_template(
                xray_id=sample_xray.id, user_id=test_user_id
            )
            assert template1 is not None

            # Second share - should return existing template
            template2 = await service.share_xray_as_template(
                xray_id=sample_xray.id, user_id=test_user_id
            )
            assert template2 is not None

            # Should be the same template (same ID)
            assert template1.id == template2.id
            assert template1.title == template2.title

            # Verify only one template exists in database
            templates = (
                await XRayTemplate.objects()
                .where(
                    XRayTemplate.owner_id == test_user_id,
                    XRayTemplate.title == sample_xray.title,
                )
                .run()
            )
            assert len(templates) == 1

        finally:
            # Cleanup
            if template1:
                await XRayTemplate.delete().where(XRayTemplate.id == template1.id)

    @pytest.mark.asyncio
    async def test_share_xray_as_template_integration_xray_not_found(
        self, service, test_user_id
    ):
        """Integration test: Handle non-existent X-Ray"""
        non_existent_xray_id = 99999

        result = await service.share_xray_as_template(
            xray_id=non_existent_xray_id, user_id=test_user_id
        )

        assert result is None

    @pytest.mark.asyncio
    async def test_share_xray_as_template_integration_wrong_owner(
        self, service, sample_xray, another_user_id
    ):
        """Integration test: Handle X-Ray owned by different user"""

        result = await service.share_xray_as_template(
            xray_id=sample_xray.id, user_id=another_user_id
        )

        # Should return None because X-Ray doesn't belong to another_user_id
        assert result is None

    @pytest.mark.asyncio
    async def test_share_xray_as_template_integration_different_users_can_share_same_xray_concept(
        self, service, test_user_id, another_user_id
    ):
        """Integration test: Different users can create templates with same content"""
        # Create two X-Rays with identical content for different users

        xray1 = None
        xray2 = None
        template1 = None
        template2 = None

        try:
            # Create identical X-Rays for different users
            xray1_data = await XRay.insert(
                XRay(
                    owner_id=test_user_id,
                    title="Shared Concept X-Ray",
                    description="Same description",
                    prompt="Same prompt",
                    icon="📊",
                    short_summary="Same summary",
                    xray_type="build",
                    visibility="user",
                    scope="all",
                    alert_channels={"notifyAuthorEmail": True},
                    is_active=True,
                )
            )
            xray1 = await XRay.objects().where(XRay.id == xray1_data[0]["id"]).first()

            xray2_data = await XRay.insert(
                XRay(
                    owner_id=another_user_id,
                    title="Shared Concept X-Ray",
                    description="Same description",
                    prompt="Same prompt",
                    icon="📊",
                    short_summary="Same summary",
                    xray_type="build",
                    visibility="user",
                    scope="all",
                    alert_channels={"notifyAuthorEmail": True},
                    is_active=True,
                )
            )
            xray2 = await XRay.objects().where(XRay.id == xray2_data[0]["id"]).first()

            # Both users share their X-Rays as templates
            template1 = await service.share_xray_as_template(
                xray_id=xray1.id, user_id=test_user_id
            )
            template2 = await service.share_xray_as_template(
                xray_id=xray2.id, user_id=another_user_id
            )

            # Both should succeed and create separate templates
            assert template1 is not None
            assert template2 is not None
            assert template1.id != template2.id  # Different templates
            assert template1.owner_id == test_user_id
            assert template2.owner_id == another_user_id
            assert template1.title == template2.title  # Same content

        finally:
            # Cleanup
            if xray1:
                await XRay.delete().where(XRay.id == xray1.id)
            if xray2:
                await XRay.delete().where(XRay.id == xray2.id)
            if template1:
                await XRayTemplate.delete().where(XRayTemplate.id == template1.id)
            if template2:
                await XRayTemplate.delete().where(XRayTemplate.id == template2.id)
