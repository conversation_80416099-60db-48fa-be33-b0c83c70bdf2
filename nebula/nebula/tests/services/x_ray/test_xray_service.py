import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
import json

from nebula.services.x_ray.xray_service import (
    XRayService,
    XRayDescriptionGenerationResult,
    XRayMetadataGenerationResult,
    XRayUpdateData,
)
from nebula.services.x_ray.xray_service import XR<PERSON><PERSON><PERSON><PERSON><PERSON>er, XRaySortBy


class TestXRayService:
    """Unit tests for XRayService"""

    @pytest.fixture
    def service(self) -> XRayService:
        """Create service instance for testing"""
        return XRayService()

    @pytest.fixture
    def mock_xray(self):
        """Create a mock XRay object"""
        xray = MagicMock()
        xray.id = 1
        xray.owner_id = 123
        xray.title = "Test X-Ray"
        xray.description = "Test description"
        xray.prompt = "Test prompt"
        xray.icon = "📊"
        xray.short_summary = "Test summary"
        xray.alert_channels = {"notifyAuthorEmail": True}
        xray.is_active = True
        xray.visibility = "user"
        xray.xray_type = "build"
        xray.scope = "all"
        xray.created_at = datetime(2024, 1, 1, 12, 0, 0)
        xray.updated_at = datetime(2024, 1, 2, 12, 0, 0)
        return xray

    @pytest.fixture
    def mock_template(self):
        """Create a mock XRayTemplate object"""
        template = MagicMock()
        template.id = 1
        template.owner_id = None
        template.title = "Test Template"
        template.description = "Test template description"
        template.prompt = "Test template prompt"
        template.icon = "📋"
        template.short_summary = "Test template summary"
        template.xray_type = "build"
        template.created_at = datetime(2024, 1, 1, 12, 0, 0)
        template.updated_at = datetime(2024, 1, 2, 12, 0, 0)
        return template

    @pytest.fixture
    def mock_openai_response(self):
        """Mock OpenAI response"""
        mock_response = MagicMock()
        mock_choice = MagicMock()
        mock_message = MagicMock()
        mock_message.content = json.dumps(
            {
                "xray_type": "build",
                "prompt": "Extract project updates and status changes from meeting discussions.",
            }
        )
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]
        return mock_response

    @pytest.mark.asyncio
    async def test_get_xrays_paginated_success(self, service: XRayService, mock_xray):
        """Test successful X-Ray pagination without filters"""
        with (
            patch("nebula.services.x_ray.xray_service.XRay") as mock_xray_model,
            patch("asyncio.gather", new_callable=AsyncMock) as mock_gather,
        ):
            # Setup mocks
            mock_gather.return_value = (5, [mock_xray, mock_xray])

            # Call service method
            xrays, total_count, has_more = await service.get_xrays_paginated(
                user_id=123, limit=10, skip=0
            )

            # Assertions
            assert len(xrays) == 2
            assert total_count == 5
            assert has_more is False  # skip(0) + limit(10) >= total_count(5)

            # Verify query building
            mock_xray_model.count.assert_called_once()
            mock_xray_model.objects.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_xrays_paginated_with_type_filter(self, service: XRayService, mock_xray):
        """Test X-Ray pagination with type filtering"""
        with (
            patch("nebula.services.x_ray.xray_service.XRay") as mock_xray_model,
            patch("asyncio.gather", new_callable=AsyncMock) as mock_gather,
        ):
            mock_gather.return_value = (3, [mock_xray])

            # Call with filter
            await service.get_xrays_paginated(
                user_id=123, xray_type=XRayTypeFilter.BUILD
            )

            # Verify filtering was applied (would be checked in the actual query building)
            mock_xray_model.count.assert_called_once()
            mock_xray_model.objects.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_xrays_paginated_with_alphabetical_sort(self, service: XRayService, mock_xray):
        """Test X-Ray pagination with alphabetical sorting"""
        with (
            patch("nebula.services.x_ray.xray_service.XRay") as mock_xray_model,
            patch("asyncio.gather", new_callable=AsyncMock) as mock_gather,
        ):
            mock_gather.return_value = (2, [mock_xray])

            # Call with alphabetical sort
            await service.get_xrays_paginated(
                user_id=123, sort_by=XRaySortBy.ALPHABETICAL
            )

            # Verify sorting was applied
            mock_xray_model.count.assert_called_once()
            mock_xray_model.objects.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_xrays_paginated_has_more_calculation(self, service: XRayService, mock_xray):
        """Test has_more calculation is correct"""
        with (
            patch("nebula.services.x_ray.xray_service.XRay") as _,
            patch("asyncio.gather", new_callable=AsyncMock) as mock_gather,
        ):
            # Test case where has_more should be True
            mock_gather.return_value = (100, [mock_xray] * 10)

            xrays, total_count, has_more = await service.get_xrays_paginated(
                user_id=123, limit=10, skip=0
            )

            assert has_more is True  # skip(0) + limit(10) < total_count(100)

            # Test case where has_more should be False
            mock_gather.return_value = (5, [mock_xray] * 5)

            xrays, total_count, has_more = await service.get_xrays_paginated(
                user_id=123, limit=10, skip=0
            )

            assert has_more is False  # skip(0) + limit(10) >= total_count(5)

    @pytest.mark.asyncio
    async def test_get_xray_templates_paginated_success(self, service: XRayService, mock_template):
        """Test successful X-Ray template pagination"""
        with (
            patch(
                "nebula.services.x_ray.xray_service.XRayTemplate"
            ) as mock_template_model,
            patch("asyncio.gather", new_callable=AsyncMock) as mock_gather,
        ):
            mock_gather.return_value = (3, [mock_template])

            (
                templates,
                total_count,
                has_more,
            ) = await service.get_xray_templates_paginated(limit=10, skip=0)

            assert len(templates) == 1
            assert total_count == 3
            assert has_more is False

            mock_template_model.count.assert_called_once()
            mock_template_model.objects.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_xray_success(self, service: XRayService, mock_xray):
        """Test successful X-Ray update"""
        with patch("nebula.services.x_ray.xray_service.XRay") as mock_xray_model:
            # Setup mock
            mock_xray_model.objects.return_value.where.return_value.first = AsyncMock(
                return_value=mock_xray
            )
            mock_xray.save = AsyncMock()

            # Test data
            update_data = XRayUpdateData(
                title="Updated Title",
                alert_channels={"notifyAuthorEmail": False},
                is_active=False,
            )

            # Call service method
            result = await service.update_xray(
                xray_id=1, user_id=123, update_data=update_data
            )

            # Assertions
            assert result == mock_xray
            assert mock_xray.title == "Updated Title"
            assert mock_xray.alert_channels == {"notifyAuthorEmail": False}
            assert mock_xray.is_active is False
            mock_xray.save.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_xray_not_found(self, service: XRayService):
        """Test X-Ray update when X-Ray doesn't exist"""
        with patch("nebula.services.x_ray.xray_service.XRay") as mock_xray_model:
            # Setup mock to return None (not found)
            mock_xray_model.objects.return_value.where.return_value.first = AsyncMock(
                return_value=None
            )

            # Call service method
            result = await service.update_xray(
                xray_id=999, user_id=123, update_data=XRayUpdateData(title="New Title")
            )

            # Should return None when not found
            assert result is None

    @pytest.mark.asyncio
    async def test_get_xray_by_id_success(self, service: XRayService, mock_xray):
        """Test successful X-Ray retrieval by ID"""
        with patch("nebula.services.x_ray.xray_service.XRay") as mock_xray_model:
            mock_xray_model.objects.return_value.where.return_value.first = AsyncMock(
                return_value=mock_xray
            )

            result = await service.get_xray_by_id(xray_id=1, user_id=123)

            assert result == mock_xray

    @pytest.mark.asyncio
    async def test_get_xray_by_id_not_found(self, service: XRayService):
        """Test X-Ray retrieval when X-Ray doesn't exist"""
        with patch("nebula.services.x_ray.xray_service.XRay") as mock_xray_model:
            mock_xray_model.objects.return_value.where.return_value.first = AsyncMock(
                return_value=None
            )

            result = await service.get_xray_by_id(xray_id=999, user_id=123)

            assert result is None

    @pytest.mark.asyncio
    async def test_field_mapping_in_update(self, service: XRayService, mock_xray):
        """Test that camelCase fields are correctly mapped to snake_case"""
        with patch("nebula.services.x_ray.xray_service.XRay") as mock_xray_model:
            mock_xray_model.objects.return_value.where.return_value.first = AsyncMock(
                return_value=mock_xray
            )
            mock_xray.save = AsyncMock()

            # Test data with camelCase fields
            update_data = XRayUpdateData(
                alert_channels={"notifyAuthorEmail": True},
                is_active=False,
            )

            await service.update_xray(xray_id=1, user_id=123, update_data=update_data)

            # Verify fields were mapped correctly
            assert mock_xray.alert_channels == {"notifyAuthorEmail": True}
            assert mock_xray.is_active is False

    @pytest.mark.asyncio
    async def test_error_handling_in_get_xrays_paginated(self, service: XRayService):
        """Test error handling in get_xrays_paginated"""
        with patch("nebula.services.x_ray.xray_service.XRay") as mock_xray_model:
            mock_xray_model.count.side_effect = Exception("Database error")

            with pytest.raises(Exception) as exc_info:
                await service.get_xrays_paginated(user_id=123)

            assert str(exc_info.value) == "Database error"

    @pytest.mark.asyncio
    async def test_error_handling_in_update_xray(self, service: XRayService):
        """Test error handling in update_xray"""
        with patch("nebula.services.x_ray.xray_service.XRay") as mock_xray_model:
            mock_xray_model.objects.return_value.where.return_value.first = AsyncMock(
                side_effect=Exception("Database error")
            )

            with pytest.raises(Exception) as exc_info:
                await service.update_xray(
                    xray_id=1,
                    user_id=123,
                    update_data=XRayUpdateData(title="New Title"),
                )

            assert str(exc_info.value) == "Database error"

    @pytest.mark.asyncio
    @patch("nebula.services.x_ray.xray_service.xray_openai_client")
    async def test_generate_step1_type_and_prompt_success(
        self, mock_client, service: XRayService, mock_openai_response
    ):
        """Test successful X-Ray generation from description"""
        # Setup mock response
        mock_response = MagicMock()
        mock_response.output_parsed = XRayDescriptionGenerationResult(
            xray_type="build",
            prompt="Extract project updates and status changes from meeting discussions.",
        )
        mock_client.responses.parse = AsyncMock(return_value=mock_response)

        # Call service method
        result = await service.generate_step1_type_and_prompt(
            "Track project status updates from meetings"
        )

        # Assertions
        assert result.xray_type == "build"
        assert "project updates" in result.prompt.lower()
        mock_client.responses.parse.assert_called_once()

    @pytest.mark.asyncio
    @patch("nebula.services.x_ray.xray_service.xray_openai_client")
    async def test_generate_xray_monitor_type(self, mock_client, service: XRayService):
        """Test X-Ray generation with monitor type"""
        # Setup mock response
        mock_response = MagicMock()
        mock_response.output_parsed = XRayDescriptionGenerationResult(
            xray_type="monitor",
            prompt="Track and log all decisions made during meetings.",
        )
        mock_client.responses.parse = AsyncMock(return_value=mock_response)

        # Call service method
        result = await service.generate_step1_type_and_prompt(
            "Track decisions made in meetings"
        )

        # Assertions
        assert result.xray_type == "monitor"
        assert "decisions" in result.prompt.lower()
        mock_client.responses.parse.assert_called_once()

    @pytest.mark.asyncio
    @patch("nebula.services.x_ray.xray_service.xray_openai_client")
    async def test_generate_xray_digest_type(self, mock_client, service: XRayService):
        """Test X-Ray generation with digest type"""
        # Setup mock response
        mock_response = MagicMock()
        mock_response.output_parsed = XRayDescriptionGenerationResult(
            xray_type="digest",
            prompt="Generate weekly summaries of team progress and updates.",
        )
        mock_client.responses.parse = AsyncMock(return_value=mock_response)

        # Call service method
        result = await service.generate_step1_type_and_prompt(
            "Create weekly team progress summaries"
        )

        # Assertions
        assert result.xray_type == "digest"
        assert "weekly" in result.prompt.lower()
        mock_client.responses.parse.assert_called_once()

    @pytest.mark.asyncio
    @patch("nebula.services.x_ray.xray_service.xray_openai_client")
    async def test_generate_xray_invalid_type_defaults_to_build(
        self, mock_client, service: XRayService
    ):
        """Test that invalid X-Ray type defaults to 'build'"""
        # Setup mock response with invalid type
        mock_response = MagicMock()
        mock_response.output_parsed = XRayDescriptionGenerationResult(
            xray_type="invalid_type",
            prompt="Track project updates.",
        )
        mock_client.responses.parse = AsyncMock(return_value=mock_response)

        # Call service method
        result = await service.generate_step1_type_and_prompt("Track project updates")

        # Assertions
        assert result.xray_type == "build"  # Should default to 'build'
        mock_client.responses.parse.assert_called_once()

    @pytest.mark.asyncio
    @patch("nebula.services.x_ray.xray_service.xray_openai_client")
    async def test_generate_xray_empty_content_error(self, mock_client, service: XRayService):
        """Test error handling for empty content"""
        # Setup mock response with empty prompt
        mock_response = MagicMock()
        mock_response.output_parsed = XRayDescriptionGenerationResult(
            xray_type="build",
            prompt="",  # Empty prompt
        )
        mock_client.responses.parse = AsyncMock(return_value=mock_response)

        # Call service method and expect ValueError
        with pytest.raises(ValueError) as exc_info:
            await service.generate_step1_type_and_prompt("Track project updates")

        assert "empty prompt" in str(exc_info.value).lower()
        mock_client.responses.parse.assert_called_once()

    @pytest.mark.asyncio
    @patch("nebula.services.x_ray.xray_service.xray_openai_client")
    async def test_generate_xray_invalid_json_error(self, mock_client, service: XRayService):
        """Test error handling for invalid JSON response"""
        # Setup mock to raise ValueError for invalid JSON
        mock_client.responses.parse = AsyncMock(
            side_effect=ValueError("Invalid JSON response")
        )

        # Call service method and expect ValueError
        with pytest.raises(ValueError) as exc_info:
            await service.generate_step1_type_and_prompt("Track project updates")

        assert "invalid json" in str(exc_info.value).lower()
        mock_client.responses.parse.assert_called_once()

    @pytest.mark.asyncio
    @patch("nebula.services.x_ray.xray_service.xray_openai_client")
    async def test_generate_xray_missing_fields_error(self, mock_client, service: XRayService):
        """Test error handling for missing required fields"""
        # Setup mock to raise ValueError for missing fields
        mock_client.responses.parse = AsyncMock(
            side_effect=ValueError("Missing required fields")
        )

        # Call service method and expect ValueError
        with pytest.raises(ValueError) as exc_info:
            await service.generate_step1_type_and_prompt("Track project updates")

        assert "missing required fields" in str(exc_info.value).lower()
        mock_client.responses.parse.assert_called_once()

    @pytest.mark.asyncio
    @patch("nebula.services.x_ray.xray_service.xray_openai_client")
    async def test_generate_xray_empty_prompt_error(self, mock_client, service: XRayService):
        """Test error handling for empty prompt"""
        # Setup mock response with empty prompt
        mock_response = MagicMock()
        mock_response.output_parsed = XRayDescriptionGenerationResult(
            xray_type="build",
            prompt="   ",  # Whitespace-only prompt
        )
        mock_client.responses.parse = AsyncMock(return_value=mock_response)

        # Call service method and expect ValueError
        with pytest.raises(ValueError) as exc_info:
            await service.generate_step1_type_and_prompt("Track project updates")

        assert "empty prompt" in str(exc_info.value).lower()
        mock_client.responses.parse.assert_called_once()

    @pytest.mark.asyncio
    @patch("nebula.services.x_ray.xray_service.xray_openai_client")
    async def test_generate_xray_openai_api_error(self, mock_client, service: XRayService):
        """Test error handling for OpenAI API errors"""
        # Setup mock to raise API error
        mock_client.responses.parse = AsyncMock(
            side_effect=Exception("OpenAI API error")
        )

        # Call service method and expect Exception
        with pytest.raises(Exception) as exc_info:
            await service.generate_step1_type_and_prompt("Track project updates")

        assert "failed to generate x-ray description" in str(exc_info.value).lower()
        mock_client.responses.parse.assert_called_once()

    # @pytest.mark.asyncio
    # async def test_prompt_template_contains_description(self, service: XRayService):
    #     """Test that the prompt template includes the description"""
    #     description = "Track project updates"
    #     formatted_prompt = service.XRAY_GENERATION_PROMPT.format(
    #         description=description
    #     )

    #     assert description in formatted_prompt
    #     assert "X-Ray type" in formatted_prompt
    #     assert "prompt" in formatted_prompt

    @pytest.mark.asyncio
    @patch("nebula.services.x_ray.xray_service.xray_openai_client")
    async def test_generate_step2_metadata_success(self, mock_client, service: XRayService):
        """Test successful X-Ray metadata generation"""
        # Setup mock response
        mock_response = MagicMock()
        mock_response.output_parsed = XRayMetadataGenerationResult(
            title="Project Status Tracker",
            emoji="🚀",
            short_summary="Track project milestones, blockers, and team updates across meetings.",
        )
        mock_client.responses.parse = AsyncMock(return_value=mock_response)

        # Call service method
        result = await service.generate_step2_metadata(
            xray_type="build",
            prompt="Track project status and updates from meetings",
        )

        # Assertions
        assert result.title == "Project Status Tracker"
        assert result.emoji == "🚀"
        assert "project" in result.short_summary.lower()
        mock_client.responses.parse.assert_called_once()

    @pytest.mark.asyncio
    @patch("nebula.services.x_ray.xray_service.xray_openai_client")
    async def test_generate_step2_metadata_title_length(self, mock_client, service: XRayService):
        """Test that title length is validated"""
        # Setup mock response with title that's too long
        mock_response = MagicMock()
        mock_response.output_parsed = XRayMetadataGenerationResult(
            title="This Is A Very Long Title That Should Be Shorter",
            emoji="🚀",
            short_summary="Track project updates.",
        )
        mock_client.responses.parse = AsyncMock(return_value=mock_response)

        # Call service method
        result = await service.generate_step2_metadata(
            xray_type="build",
            prompt="Track project updates",
        )

        # Assertions
        assert len(result.title.split()) > 4  # Should be logged as warning but not fail
        mock_client.responses.parse.assert_called_once()

    @pytest.mark.asyncio
    @patch("nebula.services.x_ray.xray_service.xray_openai_client")
    async def test_generate_step2_metadata_empty_fields(self, mock_client, service: XRayService):
        """Test error handling for empty fields"""
        # Setup mock response with empty title
        mock_response = MagicMock()
        mock_response.output_parsed = XRayMetadataGenerationResult(
            title="",  # Empty title
            emoji="🚀",
            short_summary="Track project updates.",
        )
        mock_client.responses.parse = AsyncMock(return_value=mock_response)

        # Call service method and expect ValueError
        with pytest.raises(ValueError) as exc_info:
            await service.generate_step2_metadata(
                xray_type="build",
                prompt="Track project updates",
            )

        assert "empty title" in str(exc_info.value).lower()
        mock_client.responses.parse.assert_called_once()

    @pytest.mark.asyncio
    @patch("nebula.services.x_ray.xray_service.xray_openai_client")
    async def test_generate_step2_metadata_api_error(self, mock_client, service: XRayService):
        """Test error handling for API errors"""
        # Setup mock to raise API error
        mock_client.responses.parse = AsyncMock(
            side_effect=Exception("OpenAI API error")
        )

        # Call service method and expect Exception
        with pytest.raises(Exception) as exc_info:
            await service.generate_step2_metadata(
                xray_type="build",
                prompt="Track project updates",
            )

        assert "failed to generate x-ray metadata" in str(exc_info.value).lower()
        mock_client.responses.parse.assert_called_once()

    @pytest.mark.asyncio
    @patch("nebula.services.x_ray.xray_service.xray_openai_client")
    async def test_generate_step2_metadata_different_types(self, mock_client, service: XRayService):
        """Test metadata generation for different X-Ray types"""
        test_cases = [
            (
                "build",
                "Track project status and updates",
                "Project Status Tracker",
                "🚀",
                "Track project milestones and updates",
            ),
            (
                "monitor",
                "Monitor sales pipeline changes",
                "Sales Pipeline Monitor",
                "📈",
                "Track changes in sales pipeline and deal status",
            ),
            (
                "digest",
                "Generate weekly team updates",
                "Weekly Team Digest",
                "📊",
                "Create weekly summaries of team progress and updates",
            ),
        ]

        for (
            xray_type,
            prompt,
            expected_title,
            expected_emoji,
            expected_summary,
        ) in test_cases:
            # Setup mock response
            mock_response = MagicMock()
            mock_response.output_parsed = XRayMetadataGenerationResult(
                title=expected_title,
                emoji=expected_emoji,
                short_summary=expected_summary,
            )
            mock_client.responses.parse = AsyncMock(return_value=mock_response)

            # Call service method
            result = await service.generate_step2_metadata(
                xray_type=xray_type,
                prompt=prompt,
            )

            # Assertions
            assert result.title == expected_title
            assert result.emoji == expected_emoji
            assert result.short_summary == expected_summary
            mock_client.responses.parse.assert_called_once()
            mock_client.responses.parse.reset_mock()

    # DELETE XRAY TESTS

    @pytest.mark.asyncio
    async def test_delete_xray_success(self, service: XRayService, mock_xray):
        """Test successful X-Ray deletion"""
        with patch("nebula.services.x_ray.xray_service.XRay") as mock_xray_model:
            mock_xray_model.objects.return_value.where.return_value.first = AsyncMock(
                return_value=mock_xray
            )
            mock_xray.remove = AsyncMock()

            result = await service.delete_xray(xray_id=1, user_id=123)

            assert result is True
            mock_xray.remove.assert_called_once()

    @pytest.mark.asyncio
    async def test_delete_xray_not_found(self, service: XRayService):
        """Test delete returns False when X-Ray doesn't exist"""
        with patch("nebula.services.x_ray.xray_service.XRay") as mock_xray_model:
            mock_xray_model.objects.return_value.where.return_value.first = AsyncMock(
                return_value=None
            )

            result = await service.delete_xray(xray_id=999, user_id=123)

            assert result is False

    @pytest.mark.asyncio
    async def test_delete_xray_wrong_owner(self, service: XRayService):
        """Test delete returns False when user doesn't own X-Ray"""
        with patch("nebula.services.x_ray.xray_service.XRay") as mock_xray_model:
            # Mock the query to return None (no X-Ray found for this user)
            mock_xray_model.objects.return_value.where.return_value.first = AsyncMock(
                return_value=None
            )

            result = await service.delete_xray(xray_id=1, user_id=456)

            assert result is False

    @pytest.mark.asyncio
    async def test_delete_xray_database_error(self, service: XRayService, mock_xray):
        """Test delete handles database errors gracefully"""
        with patch("nebula.services.x_ray.xray_service.XRay") as mock_xray_model:
            mock_xray_model.objects.return_value.where.return_value.first = AsyncMock(
                return_value=mock_xray
            )
            mock_xray.remove = AsyncMock(side_effect=Exception("Database error"))

            with pytest.raises(Exception) as exc_info:
                await service.delete_xray(xray_id=1, user_id=123)

            assert "Database error" in str(exc_info.value)

    # GET XRAY TEMPLATE BY ID TESTS

    @pytest.mark.asyncio
    async def test_get_xray_template_by_id_success(self, service: XRayService, mock_template):
        """Test successful template retrieval by ID"""
        with patch(
            "nebula.services.x_ray.xray_service.XRayTemplate"
        ) as mock_template_model:
            mock_template_model.objects.return_value.where.return_value.first = (
                AsyncMock(return_value=mock_template)
            )

            result = await service.get_xray_template_by_id(template_id=1)

            assert result == mock_template

    @pytest.mark.asyncio
    async def test_get_xray_template_by_id_not_found(self, service: XRayService):
        """Test template not found"""
        with patch(
            "nebula.services.x_ray.xray_service.XRayTemplate"
        ) as mock_template_model:
            mock_template_model.objects.return_value.where.return_value.first = (
                AsyncMock(return_value=None)
            )

            result = await service.get_xray_template_by_id(template_id=999)

            assert result is None

    @pytest.mark.asyncio
    async def test_get_xray_template_by_id_user_template_filtered(self, service: XRayService):
        """Test that user-owned templates are filtered out"""
        with patch(
            "nebula.services.x_ray.xray_service.XRayTemplate"
        ) as mock_template_model:
            # Mock query should only look for owner_id = null, so user templates won't be found
            mock_template_model.objects.return_value.where.return_value.first = (
                AsyncMock(return_value=None)
            )

            result = await service.get_xray_template_by_id(template_id=1)

            assert result is None
            # Verify the query includes the owner_id = null filter
            mock_template_model.objects.return_value.where.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_xray_template_by_id_database_error(self, service: XRayService):
        """Test database error handling"""
        with patch(
            "nebula.services.x_ray.xray_service.XRayTemplate"
        ) as mock_template_model:
            mock_template_model.objects.return_value.where.return_value.first = (
                AsyncMock(side_effect=Exception("Database error"))
            )

            with pytest.raises(Exception) as exc_info:
                await service.get_xray_template_by_id(template_id=1)

            assert "Database error" in str(exc_info.value)

    # SHARE XRAY AS TEMPLATE TESTS

    @pytest.mark.asyncio
    async def test_share_xray_as_template_success(self, service: XRayService, mock_xray):
        """Test successful X-Ray sharing as template"""
        user_id = 123

        with patch(
            "nebula.services.x_ray.xray_service.XRayTemplate"
        ) as mock_template_model:
            # Mock getting the X-Ray (successful)
            with patch.object(service, "get_xray_by_id") as mock_get_xray:
                mock_get_xray.return_value = mock_xray

                # Mock created template retrieval
                mock_created_template = MagicMock()
                mock_created_template.id = 1
                mock_created_template.owner_id = user_id
                mock_created_template.title = mock_xray.title

                # Mock the database query chain - need to handle two different calls
                mock_objects_1 = MagicMock()
                mock_where_1 = MagicMock()
                mock_objects_1.where.return_value = mock_where_1
                mock_where_1.first = AsyncMock(
                    return_value=None
                )  # No existing template

                mock_objects_2 = MagicMock()
                mock_where_2 = MagicMock()
                mock_objects_2.where.return_value = mock_where_2
                mock_where_2.first = AsyncMock(
                    return_value=mock_created_template
                )  # Return created template

                # Configure objects() to return different mocks for each call
                mock_template_model.objects.return_value = mock_objects_1

                # Mock the template object creation and save
                mock_template_instance = MagicMock()
                mock_template_instance.id = 1
                mock_template_instance.save = AsyncMock()
                mock_template_model.return_value = mock_template_instance

                result = await service.share_xray_as_template(
                    xray_id=1, user_id=user_id
                )

                assert result == mock_template_instance
                mock_get_xray.assert_called_once_with(xray_id=1, user_id=user_id)
                mock_template_instance.save.assert_called_once()

    @pytest.mark.asyncio
    async def test_share_xray_as_template_xray_not_found(self, service: XRayService):
        """Test sharing when X-Ray doesn't exist or doesn't belong to user"""
        user_id = 123

        with patch.object(service, "get_xray_by_id") as mock_get_xray:
            mock_get_xray.return_value = None

            result = await service.share_xray_as_template(xray_id=999, user_id=user_id)

            assert result is None
            mock_get_xray.assert_called_once_with(xray_id=999, user_id=user_id)

    @pytest.mark.asyncio
    async def test_share_xray_as_template_duplicate_prevention(
        self, service: XRayService, mock_xray
    ):
        """Test that sharing returns existing template if duplicate exists (idempotent)"""
        user_id = 123

        with patch(
            "nebula.services.x_ray.xray_service.XRayTemplate"
        ) as mock_template_model:
            # Mock getting the X-Ray (successful)
            with patch.object(service, "get_xray_by_id") as mock_get_xray:
                mock_get_xray.return_value = mock_xray

                # Mock existing template found
                mock_existing_template = MagicMock()
                mock_existing_template.id = 2
                mock_existing_template.owner_id = user_id
                mock_existing_template.title = mock_xray.title
                mock_template_model.objects.return_value.where.return_value.first = (
                    AsyncMock(return_value=mock_existing_template)
                )

                result = await service.share_xray_as_template(
                    xray_id=1, user_id=user_id
                )

                assert result == mock_existing_template
                # Should not create new template if duplicate exists
                mock_template_model.insert.assert_not_called()

    @pytest.mark.asyncio
    async def test_share_xray_as_template_field_mapping(self, service: XRayService, mock_xray):
        """Test that correct fields are copied to template"""
        user_id = 123

        with patch(
            "nebula.services.x_ray.xray_service.XRayTemplate"
        ) as mock_template_model:
            # Mock getting the X-Ray (successful)
            with patch.object(service, "get_xray_by_id") as mock_get_xray:
                mock_get_xray.return_value = mock_xray

                # Mock created template retrieval
                mock_created_template = MagicMock()

                # Mock the database query chain - need to handle two different calls
                mock_objects_1 = MagicMock()
                mock_where_1 = MagicMock()
                mock_objects_1.where.return_value = mock_where_1
                mock_where_1.first = AsyncMock(
                    return_value=None
                )  # No existing template

                mock_objects_2 = MagicMock()
                mock_where_2 = MagicMock()
                mock_objects_2.where.return_value = mock_where_2
                mock_where_2.first = AsyncMock(
                    return_value=mock_created_template
                )  # Return created template

                # Configure objects() to return different mocks for each call
                mock_template_model.objects.return_value = mock_objects_1

                # Mock the template object creation and save
                mock_template_instance = MagicMock()
                mock_template_instance.id = 1
                mock_template_instance.save = AsyncMock()
                mock_template_model.return_value = mock_template_instance

                await service.share_xray_as_template(xray_id=1, user_id=user_id)

                # Verify constructor was called with correct fields
                expected_constructor_args = {
                    "owner_id": user_id,
                    "title": mock_xray.title,
                    "description": mock_xray.description,
                    "prompt": mock_xray.prompt,
                    "icon": mock_xray.icon,
                    "short_summary": mock_xray.short_summary,
                    "xray_type": mock_xray.xray_type,
                }
                mock_template_model.assert_called_once_with(**expected_constructor_args)

    @pytest.mark.asyncio
    async def test_share_xray_as_template_database_error(self, service: XRayService):
        """Test error handling when database operations fail"""
        user_id = 123

        with patch.object(service, "get_xray_by_id") as mock_get_xray:
            mock_get_xray.side_effect = Exception("Database error")

            with pytest.raises(Exception, match="Database error"):
                await service.share_xray_as_template(xray_id=1, user_id=user_id)
