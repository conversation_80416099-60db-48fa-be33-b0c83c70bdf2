import json
from typing import List, Optional, Tu<PERSON>, Dict, Any, cast
from loguru import logger
from nebula.db.models.xray_doc_commit_notif import XRayDocumentCommitNotification
from nebula.db.pool import get_mars_pool
from nebula.services.x_ray.prompts import XRayPrompts
from nebula.web.api.x_ray.schema import (
    XRayResponse,
    xray_model_from_xray_row_dict,
)
from pydantic import BaseModel

from nebula.db.models.xray import XRay
from nebula.db.models.xray_template import XRayTemplate
from nebula.db.models.xray_doc_commit import XRayDocumentCommit
from nebula.db.models.xray_doc_line_blame import XRayDocumentLineBlame
from nebula.clients.llms import xray_openai_client
from nebula.settings import settings
from nebula.services.x_ray.types import XRayTypeFilter, XRaySortBy, XRayUpdateData


class XRayDescriptionGenerationResult(BaseModel):
    """Result model for X-Ray generation"""

    xray_type: str
    prompt: str


class XRayMetadataGenerationResult(BaseModel):
    """Result model for X-Ray metadata generation"""

    title: str
    emoji: str
    short_summary: str


class XRayService:
    """Service for handling X-Ray operations"""

    async def generate_step1_type_and_prompt(
        self, description: str
    ) -> XRayDescriptionGenerationResult:
        """
        Generate X-Ray type and prompt from user description using OpenAI.

        Args:
            description: User's description of what they want to track/analyze

        Returns:
            XRayDescriptionGenerationResult with xray_type and prompt

        Raises:
            ValueError: If LLM response is invalid or cannot be parsed
            Exception: If OpenAI API call fails
        """
        try:
            # Format the prompt with the user's description
            formatted_prompt = XRayPrompts.get_create_xray_step1_user_prompt(
                description=description
            )
            print("Step 1 formatted prompt:", formatted_prompt)

            # Call OpenAI with structured output
            response = await xray_openai_client.responses.parse(
                model=settings.openai_xray_model,
                input=[
                    {
                        "role": "system",
                        "content": XRayPrompts.get_create_xray_step1_system_prompt(),
                    },
                    {"role": "user", "content": formatted_prompt},
                ],
                text_format=XRayDescriptionGenerationResult,
                temperature=0.3,  # Some creativity but mostly deterministic
            )

            result = response.output_parsed
            if not result:
                raise ValueError("LLM returned empty response")

            print("Step 1 LLM response:", response)

            # Validate xray_type is one of the allowed values
            valid_types = {"build", "monitor", "digest"}
            if result.xray_type not in valid_types:
                logger.warning(
                    f"LLM returned invalid xray_type: {result.xray_type}, defaulting to 'build'"
                )
                result.xray_type = "build"

            if not result.prompt.strip():
                raise ValueError("LLM returned empty prompt")

            return result

        except Exception as e:
            logger.error(f"Error generating X-Ray from description: {e}")
            if isinstance(e, ValueError):
                raise e
            raise Exception(f"Failed to generate X-Ray description: {str(e)}")

    async def generate_step2_metadata(
        self, xray_type: str, prompt: str
    ) -> XRayMetadataGenerationResult:
        """
        Generate X-Ray metadata (title, emoji, short summary) from X-Ray type and prompt.

        Args:
            xray_type: Type of the X-Ray (build, monitor, digest)
            prompt: The prompt that will be used to analyze meetings

        Returns:
            XRayMetadataGenerationResult with title, emoji, and short_summary

        Raises:
            ValueError: If LLM response is invalid or cannot be parsed
            Exception: If OpenAI API call fails
        """
        try:
            response = await xray_openai_client.responses.parse(
                model=settings.openai_xray_model,
                input=[
                    {
                        "role": "system",
                        "content": XRayPrompts.get_create_xray_step2_system_prompt(),
                    },
                    {
                        "role": "user",
                        "content": XRayPrompts.get_create_xray_step2_user_prompt(
                            xray_type=xray_type, prompt=prompt
                        ),
                    },
                ],
                text_format=XRayMetadataGenerationResult,
                temperature=0.7,  # More creative for emoji and title generation
            )

            result = response.output_parsed
            if not result:
                raise ValueError("LLM returned empty response")

            # Validate fields
            if not result.title.strip():
                raise ValueError("LLM returned empty title")
            if not result.emoji.strip():
                raise ValueError("LLM returned empty emoji")
            if not result.short_summary.strip():
                raise ValueError("LLM returned empty short summary")

            # Validate title length (3-4 words)
            word_count = len(result.title.split())
            if word_count < 2 or word_count > 4:
                logger.warning(
                    f"LLM returned title with {word_count} words, expected 3-4 words"
                )

            return result

        except Exception as e:
            logger.error(f"Error generating X-Ray metadata: {e}")
            if isinstance(e, ValueError):
                raise e
            raise Exception(f"Failed to generate X-Ray metadata: {str(e)}")

    async def get_active_xrays_no_digests_paginated(
        self,
        user_id: int,
        limit: int = 16,
        skip: int = 0,
        xray_type: Optional[XRayTypeFilter] = None,
        sort_by: XRaySortBy = XRaySortBy.LAST_UPDATED,
    ) -> Tuple[List[XRayResponse.XRay], int, bool]:
        """
        Get paginated X-Rays excluding digests with filtering and sorting, with unread notifications count

        Args:
            user_id: ID of the user requesting X-Rays
            limit: Maximum number of results to return
            skip: Number of results to skip for pagination
            xray_type: Optional filter by X-Ray type
            sort_by: Sorting option (last_updated or alphabetical)

        Returns:
            Tuple of (xrays_list, total_count, has_more)
        """
        try:
            # Build base query
            base_query = XRay.owner_id == user_id

            # Add filtering by xray_type if provided
            if xray_type:
                base_query = base_query & (XRay.xray_type == xray_type.value)

            # Get total count with filters
            count_query = XRay.count().where(base_query)

            order_by = "updated_at DESC"
            if sort_by == XRaySortBy.ALPHABETICAL:
                order_by = "title ASC"

            # Get unread notifications count
            xrays_with_notif_count_query = XRayDocumentCommitNotification.raw(f"""
                SELECT xr.*, COUNT(notif.id) AS unread_notifications_count
                FROM xrays xr
                LEFT JOIN xray_doc_commits comm ON comm.xray_id = xr.id
                LEFT JOIN xray_doc_commit_notifications notif 
                    ON notif.xray_doc_commit_id = comm.id AND notif.seen = false
                WHERE xr.owner_id = {user_id} AND xr.xray_type != 'digest' AND xr.is_active = TRUE
                GROUP BY xr.id
                ORDER BY {order_by}
                LIMIT {limit}
                OFFSET {skip}
            """)

            # Execute queries concurrently
            import asyncio

            total_count, xrays_with_notif_count = await asyncio.gather(
                count_query, xrays_with_notif_count_query
            )
            xrays_with_notif_count = cast(List[Dict[str, Any]], xrays_with_notif_count)

            has_more = (skip + limit) < total_count

            return (
                [
                    xray_model_from_xray_row_dict(xray)
                    for xray in xrays_with_notif_count
                ],
                total_count,
                has_more,
            )

        except Exception as e:
            logger.error(f"Error in get_xrays_paginated: {e}")
            raise

    async def get_xrays_paginated(
        self,
        user_id: int,
        limit: int = 16,
        skip: int = 0,
        xray_type: Optional[XRayTypeFilter] = None,
        sort_by: XRaySortBy = XRaySortBy.LAST_UPDATED,
    ) -> Tuple[List[XRayResponse.XRay], int, bool]:
        """
        Get paginated X-Rays with filtering and sorting, with unread notifications count

        Args:
            user_id: ID of the user requesting X-Rays
            limit: Maximum number of results to return
            skip: Number of results to skip for pagination
            xray_type: Optional filter by X-Ray type
            sort_by: Sorting option (last_updated or alphabetical)

        Returns:
            Tuple of (xrays_list, total_count, has_more)
        """
        try:
            # Build base query
            base_query = XRay.owner_id == user_id

            # Add filtering by xray_type if provided
            if xray_type:
                base_query = base_query & (XRay.xray_type == xray_type.value)

            # Get total count with filters
            count_query = XRay.count().where(base_query)

            order_by = "updated_at DESC"
            if sort_by == XRaySortBy.ALPHABETICAL:
                order_by = "title ASC"

            # Get unread notifications count
            xrays_with_notif_count_query = XRayDocumentCommitNotification.raw(f"""
                SELECT xr.*, COUNT(notif.id) AS unread_notifications_count
                FROM xrays xr
                LEFT JOIN xray_doc_commits comm ON comm.xray_id = xr.id
                LEFT JOIN xray_doc_commit_notifications notif 
                    ON notif.xray_doc_commit_id = comm.id AND notif.seen = false
                WHERE xr.owner_id = {user_id}
                GROUP BY xr.id
                ORDER BY {order_by}
                LIMIT {limit}
                OFFSET {skip}
            """)

            # Execute queries concurrently
            import asyncio

            total_count, xrays_with_notif_count = await asyncio.gather(
                count_query, xrays_with_notif_count_query
            )
            xrays_with_notif_count = cast(List[Dict[str, Any]], xrays_with_notif_count)

            has_more = (skip + limit) < total_count

            return (
                [
                    xray_model_from_xray_row_dict(xray)
                    for xray in xrays_with_notif_count
                ],
                total_count,
                has_more,
            )

        except Exception as e:
            logger.error(f"Error in get_xrays_paginated: {e}")
            raise

    async def get_xray_templates_paginated(
        self,
        limit: int = 16,
        skip: int = 0,
    ) -> Tuple[List[XRayTemplate], int, bool]:
        """
        Get paginated X-Ray templates (Rumi-generated templates only)

        Args:
            limit: Maximum number of results to return
            skip: Number of results to skip for pagination

        Returns:
            Tuple of (templates_list, total_count, has_more)
        """
        try:
            # Build query to get templates where owner_id is null (Rumi-generated only)
            base_query = XRayTemplate.owner_id.is_null()

            # Get total count with filters
            count_query = XRayTemplate.count().where(base_query)

            # Get paginated results with filters
            templates_query = (
                XRayTemplate.objects()
                .where(base_query)
                .order_by(XRayTemplate.updated_at, ascending=False)
                .limit(limit)
                .offset(skip)
            )

            # Execute queries concurrently
            import asyncio

            total_count, templates = await asyncio.gather(count_query, templates_query)

            # Calculate if there are more results
            has_more = (skip + limit) < total_count

            return templates, total_count, has_more

        except Exception as e:
            logger.error(f"Error in get_xray_templates_paginated: {e}")
            raise

    async def get_xray_template_by_id(self, template_id: int) -> Optional[XRayTemplate]:
        """
        Get an X-Ray template by ID (Rumi-generated templates only).

        Args:
            template_id: ID of the template to get

        Returns:
            XRayTemplate object if found and is Rumi-generated (owner_id = null), None otherwise

        Raises:
            Exception: If database operation fails
        """
        try:
            template = (
                await XRayTemplate.objects()
                .where(XRayTemplate.id == template_id)
                .first()
            )

            return template

        except Exception as e:
            logger.error(f"Error getting X-Ray template {template_id}: {e}")
            raise

    async def update_xray(
        self, xray_id: int, user_id: int, update_data: XRayUpdateData
    ) -> Optional[XRay]:
        """
        Update an X-Ray with the given data

        Args:
            xray_id: ID of the X-Ray to update
            user_id: ID of the user making the update (for authorization)
            update_data: Typed update data for the X-Ray

        Returns:
            Updated X-Ray object or None if not found
        """
        try:
            # Find the X-Ray and ensure it belongs to the user
            xray = (
                await XRay.objects()
                .where(XRay.id == xray_id, XRay.owner_id == user_id)
                .first()
            )

            if not xray:
                return None

            # Update fields - only update fields that were explicitly set
            update_dict = update_data.model_dump(exclude_unset=True)

            # Check if this is a digest X-Ray and if frequency/timezone is being updated
            is_digest_xray = xray.xray_type == "digest"
            frequency_changed = (
                "frequency" in update_dict
                and update_dict["frequency"] != xray.frequency
            )
            timezone_changed = (
                "timezone" in update_dict and update_dict["timezone"] != xray.timezone
            )

            for field, value in update_dict.items():
                setattr(xray, field, value)

            # Manually update the updated_at timestamp
            from datetime import datetime

            xray.updated_at = datetime.now()

            await xray.save()

            # Handle digest schedule updates for digest X-Rays
            if is_digest_xray and (frequency_changed or timezone_changed):
                try:
                    if xray.frequency:
                        # Update the Temporal schedule with new frequency
                        await self.update_digest_schedule(xray, xray.frequency)
                        logger.info(f"Updated digest schedule for X-Ray {xray_id}")
                    else:
                        logger.warning(
                            f"Frequency cleared for digest X-Ray {xray_id}, schedule may need manual cleanup"
                        )
                except Exception as e:
                    logger.error(
                        f"Failed to update digest schedule for X-Ray {xray_id}: {e}"
                    )
                    # Don't fail the update if schedule update fails
                    # User can retry schedule update later

            return xray

        except Exception as e:
            logger.error(f"Error in update_xray: {e}")
            raise

    async def get_xray_by_id(self, xray_id: int, user_id: int) -> Optional[XRay]:
        """
        Get an X-Ray by ID.

        Args:
            xray_id: ID of the X-Ray to get
            user_id: ID of the user requesting the X-Ray

        Returns:
            XRay object if found, None otherwise
        """
        return (
            await XRay.objects()
            .where(XRay.id == xray_id, XRay.owner_id == user_id)
            .first()
        )

    async def delete_xray(self, xray_id: int, user_id: int) -> bool:
        """
        Delete an X-Ray by ID.

        Args:
            xray_id: ID of the X-Ray to delete
            user_id: ID of the user requesting the deletion (for authorization)

        Returns:
            True if X-Ray was deleted, False if not found or user doesn't own it

        Raises:
            Exception: If database operation fails
        """
        try:
            # Find the X-Ray and ensure it belongs to the user
            xray = (
                await XRay.objects()
                .where(XRay.id == xray_id, XRay.owner_id == user_id)
                .first()
            )

            if not xray:
                return False

            # Delete the X-Ray (related records should be handled by CASCADE)
            await xray.remove()

            logger.info(f"Successfully deleted X-Ray {xray_id} for user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Error deleting X-Ray {xray_id}: {e}")
            raise

    async def create_xray(
        self,
        user_id: int,
        description: str,
        xray_type: str,
        prompt: str,
        title: str,
        emoji: str,
        short_summary: str,
        timezone: Optional[str] = None,
        alert_channels: Optional[Dict[str, bool]] = None,
        cron_expression: Optional[str] = None,
    ) -> XRay:
        """
        Create a new X-Ray.

        Args:
            user_id: ID of the user creating the X-Ray
            description: User's original description
            xray_type: Type of the X-Ray (build, monitor, digest)
            prompt: The prompt that will be used to analyze meetings
            title: Title of the X-Ray
            emoji: Emoji representing the X-Ray
            short_summary: Short summary of what the X-Ray does
            timezone: Timezone identifier for digest scheduling (required for digest X-Rays)
            cron_expression: Cron expression for digest scheduling (required for digest X-Rays)

        Returns:
            Created XRay object

        Raises:
            ValueError: If any required field is empty
            Exception: If database operation fails
        """
        try:
            # Validate required fields
            if not description.strip():
                raise ValueError("Description cannot be empty")
            if not xray_type.strip():
                raise ValueError("X-Ray type cannot be empty")
            if not prompt.strip():
                raise ValueError("Prompt cannot be empty")
            if not title.strip():
                raise ValueError("Title cannot be empty")
            if not emoji.strip():
                raise ValueError("Emoji cannot be empty")
            if not short_summary.strip():
                raise ValueError("Short summary cannot be empty")

            # Additional validation for digest X-Rays
            if xray_type == "digest":
                if not timezone:
                    raise ValueError("Timezone is required for digest X-Rays")
                if not cron_expression:
                    raise ValueError("Cron expression is required for digest X-Rays")

            # Create X-Ray record
            xray = XRay(
                owner_id=user_id,
                description=description,
                xray_type=xray_type,
                prompt=prompt,
                title=title,
                icon=emoji,
                short_summary=short_summary,
                alert_channels=json.dumps(alert_channels) if alert_channels else None,
                is_active=True,  # Default to active
                visibility="user",  # Default to user visibility
                scope="all",  # Default to all scope (personal + team visible)
                timezone=timezone,  # Store timezone for digest scheduling
                frequency=cron_expression,  # Store cron expression for digest scheduling
            )
            await xray.save()

            # Create Temporal schedule for digest X-Rays
            if xray_type == "digest" and cron_expression:
                try:
                    schedule_id = await self.create_digest_schedule(
                        xray, cron_expression
                    )
                    logger.info(
                        f"Created digest schedule {schedule_id} for X-Ray {xray.id}"
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to create digest schedule for X-Ray {xray.id}: {e}"
                    )
                    # Don't fail the X-Ray creation if schedule creation fails
                    # User can retry schedule creation later

            return xray

        except Exception as e:
            logger.error(f"Error creating X-Ray: {e}")
            if isinstance(e, ValueError):
                raise e
            raise Exception(f"Failed to create X-Ray: {str(e)}")

    async def share_xray_as_template(
        self, xray_id: int, user_id: int
    ) -> Optional[XRayTemplate]:
        """
        Share an X-Ray as a template. Creates a template from the X-Ray's settings,
        checking for duplicates first. User templates are only discoverable via direct URLs.

        Args:
            xray_id: ID of the X-Ray to share as template
            user_id: ID of the user sharing the X-Ray (for authorization)

        Returns:
            XRayTemplate object (existing or newly created), None if X-Ray not found

        Raises:
            Exception: If database operation fails
        """
        try:
            # First, get the X-Ray and ensure it belongs to the user
            xray = await self.get_xray_by_id(xray_id=xray_id, user_id=user_id)

            if not xray:
                return None

            # Check if a template with the same fields already exists for this user
            # We match on core fields to detect duplicates
            existing_template = (
                await XRayTemplate.objects()
                .where(
                    XRayTemplate.owner_id == user_id,
                    XRayTemplate.title == xray.title,
                    XRayTemplate.description == xray.description,
                    XRayTemplate.prompt == xray.prompt,
                    XRayTemplate.xray_type == xray.xray_type,
                )
                .first()
            )

            if existing_template:
                logger.info(
                    f"Template already exists for X-Ray {xray_id}, returning existing template {existing_template.id}"
                )
                return existing_template

            # Create new template by copying specific fields
            # Note: XRayTemplate doesn't have visibility/scope fields, so we only copy what exists
            new_template = XRayTemplate(
                owner_id=user_id,  # User-generated template
                title=xray.title,
                description=xray.description,
                prompt=xray.prompt,
                icon=xray.icon,
                short_summary=xray.short_summary,
                xray_type=xray.xray_type,
            )
            await new_template.save()

            logger.info(f"Created new template {new_template.id} from X-Ray {xray_id}")

            return new_template

        except Exception as e:
            logger.error(f"Error sharing X-Ray {xray_id} as template: {e}")
            raise

    async def get_xray_with_current_commit(
        self, xray_id: int
    ) -> Optional[Tuple[XRay, Optional[XRayDocumentCommit]]]:
        """Get X-Ray with its current commit content."""

        xray = await XRay.objects().where(XRay.id == xray_id).first()
        if not xray:
            return None

        current_commit = None
        if xray.current_commit_id:
            current_commit = (
                await XRayDocumentCommit.objects()
                .where(XRayDocumentCommit.id == xray.current_commit_id)
                .first()
            )

        return (xray, current_commit)

    async def create_commit_and_update_xray(
        self,
        xray_id: int,
        content: str,
        author_id: int,
        recurrence_id: Optional[int] = None,
    ) -> "XRayDocumentCommit":
        """Create a new commit and update X-Ray's current_commit_id."""

        # Create the commit
        commit = XRayDocumentCommit(
            xray_id=xray_id, author_id=author_id, content=content
        )
        await commit.save()

        # Update the X-Ray's current_commit_id
        await XRay.update({XRay.current_commit_id: commit.id}).where(XRay.id == xray_id)

        # Update line blame tracking
        lines = content.split("\n")
        for line_num, line_content in enumerate(lines):
            blame = XRayDocumentLineBlame(
                xray_id=xray_id,
                commit_id=commit.id,
                line_number=line_num,
                content=line_content,
                author_id=author_id,
            )
            await blame.save()

        return commit

    async def get_xray_content_with_blame(
        self, xray_id: int
    ) -> Tuple[str, Dict[int, Dict[str, Any]]]:
        """Get X-Ray content with blame information."""

        xray = await XRay.objects().where(XRay.id == xray_id).first()
        if not xray or not xray.current_commit_id:
            return "", {}

        commit = (
            await XRayDocumentCommit.objects()
            .where(XRayDocumentCommit.id == xray.current_commit_id)
            .first()
        )

        if not commit:
            return "", {}

        # Get blame info (simplified for now)
        blame_info = {}
        # TODO: Implement proper blame info retrieval from XRayDocumentLineBlame

        return commit.content, blame_info

    async def get_xray_notifications_paginated(
        self, xray_id: int, user_id: int, limit: int = 30, offset: int = 0
    ) -> Tuple[List[Dict[str, Any]], int, bool]:
        """
        Get paginated notifications for a specific X-Ray

        Args:
            xray_id: ID of the X-Ray to get notifications for
            user_id: ID of the user requesting notifications (for authorization)
            limit: Maximum number of notifications to return
            offset: Number of notifications to skip

        Returns:
            Tuple of (notifications list, total count, has_more flag)
        """
        try:
            # Verify user owns the X-Ray
            xray = (
                await XRay.objects()
                .where(XRay.id == xray_id, XRay.owner_id == user_id)
                .first()
            )

            if not xray:
                return [], 0, False

            # Use raw SQL for the complex query with joins, similar to existing patterns
            notifications_query = XRayDocumentCommitNotification.raw(f"""
                SELECT notif.*
                FROM xray_doc_commit_notifications notif
                JOIN xray_doc_commits comm ON notif.xray_doc_commit_id = comm.id
                WHERE comm.xray_id = {xray_id} AND notif.user_id = {user_id}
                ORDER BY notif.created_at DESC
                LIMIT {limit}
                OFFSET {offset}
            """)

            # Get total count for this X-Ray and user
            count_query = XRayDocumentCommitNotification.raw(f"""
                SELECT COUNT(*) as count
                FROM xray_doc_commit_notifications notif
                JOIN xray_doc_commits comm ON notif.xray_doc_commit_id = comm.id
                WHERE comm.xray_id = {xray_id} AND notif.user_id = {user_id}
            """)

            # Execute queries concurrently
            import asyncio

            notifications_result, count_result = await asyncio.gather(
                notifications_query, count_query
            )

            notifications = cast(List[Dict[str, Any]], notifications_result)
            total_count = count_result[0]["count"] if count_result else 0

            has_more = offset + len(notifications) < total_count

            return notifications, total_count, has_more

        except Exception as e:
            logger.error(f"Error getting X-Ray notifications: {e}")
            raise

    async def mark_xray_notifications_seen(self, xray_id: int, user_id: int) -> int:
        """
        Mark all unseen notifications for an X-Ray as seen

        Args:
            xray_id: ID of the X-Ray to mark notifications for
            user_id: ID of the user marking notifications (for authorization)

        Returns:
            Number of notifications marked as seen
        """
        try:
            # Verify user owns the X-Ray
            xray = (
                await XRay.objects()
                .where(XRay.id == xray_id, XRay.owner_id == user_id)
                .first()
            )

            if not xray:
                return 0

            from datetime import datetime

            update_result = await XRayDocumentCommitNotification.raw(f"""
                UPDATE xray_doc_commit_notifications 
                SET seen = true, updated_at = '{datetime.now().isoformat()}'
                WHERE xray_doc_commit_id IN (
                    SELECT comm.id 
                    FROM xray_doc_commits comm 
                    WHERE comm.xray_id = {xray_id}
                ) 
                AND user_id = {user_id} 
                AND seen = false
                RETURNING id
            """)

            return len(cast(List[Any], update_result)) if update_result else 0

        except Exception as e:
            logger.error(f"Error marking X-Ray notifications as seen: {e}")
            raise

    # === DIGEST-SPECIFIC METHODS ===

    async def create_digest_schedule(self, xray: XRay, cron_expression: str) -> str:
        """
        Create a Temporal schedule for digest X-Ray.

        Args:
            xray: The digest X-Ray to create schedule for
            cron_expression: Cron expression for scheduling

        Returns:
            Schedule ID from Temporal

        Raises:
            ValueError: If X-Ray is not digest type or missing timezone
            Exception: If Temporal schedule creation fails
        """
        try:
            if xray.xray_type != "digest":
                raise ValueError("Can only create schedules for digest X-Rays")

            if not xray.timezone:
                raise ValueError("Timezone is required for digest scheduling")

                # Import Temporal client and types
            from nebula.temporal.client import create_temporal_client
            from temporalio.client import (
                Schedule,
                ScheduleSpec,
                ScheduleActionStartWorkflow,
                ScheduleState,
            )
            from nebula.temporal.workflows.xray_digest_wf import XRayDigestWorkflow
            from nebula.temporal.types import XRayDigestWorkflowParams

            client = await create_temporal_client()
            schedule_id = f"xray-digest-{xray.id}"

            await client.create_schedule(
                schedule_id,
                Schedule(
                    action=ScheduleActionStartWorkflow(
                        XRayDigestWorkflow.run,
                        XRayDigestWorkflowParams(xray_id=xray.id),
                        id=f"xray-digest-workflow-{xray.id}",
                        task_queue=settings.temporal_ai_end_meeting_queue_with_prefix,  # Same queue as regular X-Ray workflows
                    ),
                    spec=ScheduleSpec(cron_expressions=[cron_expression]),
                    state=ScheduleState(
                        note=f"Digest generation for X-Ray: {xray.title}"
                    ),
                ),
            )

            logger.info(
                f"Created Temporal schedule {schedule_id} for digest X-Ray {xray.id}"
            )
            return schedule_id

        except Exception as e:
            logger.error(f"Failed to create digest schedule for X-Ray {xray.id}: {e}")
            raise Exception(f"Failed to create digest schedule: {str(e)}")

    async def delete_digest_schedule(self, schedule_id: str) -> bool:
        """
        Delete a Temporal schedule for digest X-Ray.

        Args:
            schedule_id: Temporal schedule ID to delete

        Returns:
            True if successfully deleted, False otherwise
        """
        try:
            from nebula.temporal.client import create_temporal_client

            client = await create_temporal_client()

            # Get the schedule handle and delete it
            schedule_handle = client.get_schedule_handle(schedule_id)
            await schedule_handle.delete()

            logger.info(f"Deleted Temporal schedule {schedule_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to delete digest schedule {schedule_id}: {e}")
            return False

    async def update_digest_schedule(
        self, xray: XRay, new_cron_expression: str
    ) -> bool:
        """
        Update a Temporal schedule for digest X-Ray.

        Args:
            xray: The digest X-Ray to update schedule for
            new_cron_expression: New cron expression

        Returns:
            True if successfully updated, False otherwise
        """
        try:
            if xray.xray_type != "digest":
                raise ValueError("Can only update schedules for digest X-Rays")

            schedule_id = f"xray-digest-{xray.id}"

            # Delete old schedule and create new one
            # (Temporal doesn't have direct schedule update, so we recreate)
            await self.delete_digest_schedule(schedule_id)
            new_schedule_id = await self.create_digest_schedule(
                xray, new_cron_expression
            )

            return new_schedule_id == schedule_id

        except Exception as e:
            logger.error(f"Failed to update digest schedule for X-Ray {xray.id}: {e}")
            return False

    async def generate_digest_content(self, xray: XRay, lookback_days: int = 7) -> str:
        """
        Generate digest content for a digest X-Ray using past meetings.

        Args:
            xray: The digest X-Ray to generate content for
            lookback_days: Number of days to look back for meetings

        Returns:
            Generated digest content

        Raises:
            ValueError: If X-Ray is not digest type
            Exception: If content generation fails
        """
        try:
            if xray.xray_type != "digest":
                raise ValueError("Can only generate digest content for digest X-Rays")

            # Import here to avoid circular imports
            from nebula.services.search.common import (
                get_past_user_session_recurrences_by_user_ids,
            )
            from datetime import datetime
            import pytz

            # Get timezone for date filtering
            user_tz = pytz.timezone(xray.timezone) if xray.timezone else pytz.UTC

            # Calculate date range
            now = datetime.now(user_tz)

            # Fetch past meetings based on X-Ray scope
            mars_pool = await get_mars_pool()
            past_meetings = await get_past_user_session_recurrences_by_user_ids(
                user_ids=[xray.owner_id],
                limit=50,  # Reasonable limit for digest processing
                offset=0,
                mars_pool=mars_pool,
            )

            # Filter meetings by date range if we have timestamps
            # TODO: Add proper date filtering once we understand the meeting data structure

            if not past_meetings:
                return "No meetings found in the specified time period."

            # Create a simple digest based on the meetings
            digest_content = f"""# {xray.title} - Digest Summary

Generated on: {now.strftime("%Y-%m-%d %H:%M:%S %Z")}

## Recent Meetings ({len(past_meetings)} found)

"""

            # Add basic information from meetings
            for meeting in past_meetings[:5]:  # Limit to 5 most recent
                title = meeting.get("title", "Untitled Meeting")
                digest_content += f"- {title}\n"

            digest_content += f"\n*This is a simplified digest. Full LLM-powered digest generation will be implemented in the workflow.*"

            # Update last_digest_at timestamp
            await XRay.update({XRay.last_digest_at: now}).where(XRay.id == xray.id)

            return digest_content

        except Exception as e:
            logger.error(f"Failed to generate digest content for X-Ray {xray.id}: {e}")
            raise Exception(f"Failed to generate digest content: {str(e)}")
