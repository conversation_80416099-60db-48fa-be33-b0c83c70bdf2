import os

from piccolo.conf.apps import AppConfig, table_finder

CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(__file__))


# Piccolo AppConfig
# This object is consumed by the Piccolo CLI, runtime, and test runners.
# It helps bootstrap the engine, run migrations, and perform other configuration tasks.
APP_CONFIG = AppConfig(
    app_name="nebula",
    migrations_folder_path=os.path.join(
        CURRENT_DIRECTORY,
        "migrations",
    ),
    table_classes=table_finder(
        modules=[
            "nebula.db.models.ai_feed",
            "nebula.db.models.post_session_summary",
            "nebula.db.models.thread",
            "nebula.db.models.message",
            "nebula.db.models.user_suggestion",
            "nebula.db.models.suggestion",
            "nebula.db.models.meeting_metadata",
            "nebula.db.models.xray",
            "nebula.db.models.xray_template",
            "nebula.db.models.xray_doc_commit",
            "nebula.db.models.xray_doc_commit_notif",
            "nebula.db.models.xray_doc_line_blame",
        ],
    ),
    migration_dependencies=[],
    commands=[],
)
