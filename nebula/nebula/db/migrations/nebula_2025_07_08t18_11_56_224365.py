from piccolo.apps.migrations.auto.migration_manager import MigrationManager
from piccolo.table import Table


# dummy table used for raw sql migrations
class RawTable(Table):
    pass


ID = "2025-07-08T18:11:56:224365"
VERSION = "1.23.0"
DESCRIPTION = "Insert initial xray templates into the database"

# Table names
xray_template_table_name = "xray_templates"


async def forwards():
    manager = MigrationManager(
        migration_id=ID,
        app_name="nebula",
        description=DESCRIPTION,
    )

    async def insert_xray_templates():
        # Insert initial xray templates
        await RawTable.raw(f"""
            INSERT INTO {xray_template_table_name} (
                owner_id,
                xray_type,
                icon,
                title,
                description,
                prompt,
                short_summary,
                created_at,
                updated_at
            ) VALUES
            (
                NULL,
                'build',
                '🦄',
                'Personal Workstyle X-Ray',
                'Continuously build a document about my work habits, communication style, strengths, and growth areas—highlighting patterns to help me improve and develop over time.',
                '1. Track Work Habits:\n    - Identify recurring patterns in how I approach tasks, manage time, and organize projects.\n    - Summarize routines, methods, and tools I use most often.\n2. Analyze Communication Style:\n    - Capture how I share updates, give feedback, and participate in meetings.\n    - Note my preferred ways of collaborating and resolving misunderstandings.\n3. Highlight Strengths:\n    - Surface examples where I demonstrated leadership, problem-solving, or creative thinking.\n    - Identify positive feedback or recognition I\'ve received from others.\n4. Spot Growth Areas:\n    - Point out topics I revisit, areas where I ask for support, or challenges that recur.\n    - Highlight moments of uncertainty, hesitation, or requests for clarification.\n5. Detect Change Over Time:\n    - Track how my habits, style, and strengths evolve across meetings and projects.\n    - Note any improvements or shifts in approach.\n6. Summarize in a Living Document:\n    - Regularly update a document that organizes these insights into clear sections.\n    - Structure the document to show both patterns and new observations as I grow.\n7. Actionable Suggestions:\n    - When possible, gently recommend areas to focus on for further improvement—based on trends in my meetings and interactions.',
                'Continuously build a document about my work habits, communication style, strengths, and growth areas—highlighting patterns to help me improve and develop over time.',
                NOW(),
                NOW()
            ),
            (
                NULL,
                'monitor',
                '🌻',
                'Sales and Client Monitor',
                'Receive instant alerts for major sales decisions, raised blockers, objections, and pipeline changes in client deals.',
                '1. Monitor Key Discussions:\n    - Track meetings on critical sales decisions and strategy shifts.\n    - Identify new sales tactics discussed.\n2. Detect Deal Progress:\n    - Listen for updates on deal stages and client status changes.\n    - Capture new or renewed client engagements.\n3. Identify Blockers and Objections:\n    - Detect challenges in closing deals.\n    - Highlight client objections and negotiation concerns.\n    - Track resource discussions for overcoming challenges.\n4. Capture Pipeline Changes:\n    - Note pipeline adjustments, including new or closed deals.\n    - Record changes in deal values and expected close dates.\n5. Highlight Major Decisions:\n    - Capture decisions on sales targets, quotas, and territories.\n    - Track executive decisions affecting sales operations.\n6. Real-Time Notifications:\n    - Provide immediate alerts for any met criteria.\n    - Summarize critical updates clearly and actionably.',
                'Receive instant alerts for major sales decisions, raised blockers, objections, and pipeline changes in client deals.',
                NOW(),
                NOW()
            ),
            (
                NULL,
                'digest',
                '🧬',
                'Daily Digest',
                'Generate a daily digest summarizing my team\'s key discussions, major decisions, and action items from the past 24 hours—focusing on updates, priorities, and blockers, not routine details.',
                '1. Create a daily digest from my team\'s meetings.\n2. Capture Key Elements:\n    - Focus on important discussions, new ideas, and major decisions made in the last 24 hours.\n    - List any action items assigned to me.\n3. Include Meeting Highlights:\n    - Provide highlights from each meeting, skipping routine status updates or minor details.\n    - Note any follow-up requirements or deadlines.\n4. Identify Patterns and Blockers:\n    - Point out any recurring themes, team-wide blockers, or urgent priorities that came up in multiple meetings.\n5. Specify the Time Frame:\n    - Only include meetings held in the previous 24 hours.\n6. Focus on What Matters:\n    - Emphasize information that helps me catch up quickly—summarize what\'s important for progress and next steps, without unnecessary detail.',
                'Generate a daily digest summarizing my team\'s key discussions, major decisions, and action items from the past 24 hours—focusing on updates, priorities, and blockers, not routine details.',
                NOW(),
                NOW()
            ),
            (
                NULL,
                'build',
                '🕵️‍♂️',
                'Competitive Intelligence Insights',
                'Continuously build a document that captures and summarizes important competitor moves, feature updates, positioning shifts, and trends—highlighting what matters and why for my team\'s strategy.',
                '1. **Track Key Competitor Updates:**\n    - Summarize product releases, strategy changes, key hires, funding rounds, and major pricing shifts discussed in meetings.\n    - Capture important news or public announcements about competitors.\n2. **Compare and Analyze:**\n    - Identify and summarize comparisons between your company and competitors discussed by the team.\n    - Highlight feature gaps, messaging differences, and shifts in positioning.\n3. **Spot Market Trends:**\n    - Detect recurring themes, emerging threats, or opportunities mentioned across multiple meetings.\n    - Note trends impacting your product, sales, or customer experience.\n4. **Prepare Battlecards & Briefs:**\n    - Collect insights useful for sales battlecards, objection handling, and strategic positioning.\n    - Organize notes in an executive-ready format (use clear sections, bullets, and tables as needed).\n5. **Highlight "Why It Matters":**\n    - For every major competitor action or trend, explain its potential impact on your strategy, product, or messaging.\n    - Prioritize actionable insights over generic news.\n6. **Keep the Document Updated:**\n    - Regularly update the competitive intelligence doc with new findings and changes.\n    - Structure the document for easy scanning by executives and sales leads.\n7. **Clarify and Ask When Needed:**\n    - If a company or competitor is unclear, note for follow-up or clarification.\n    - Only include externally verified facts; do not speculate.\n8. **Tone:**\n    - Professional, concise, and strategic—focus on actionable insights and clarity.\n9. **Example Document Sections:**\n    - Recent Competitor Moves\n    - Comparison Table\n    - Market Trends\n    - Battlecards / Sales Briefs\n    - Why This Matters / Strategic Actions',
                'Continuously build a document that captures and summarizes important competitor moves, feature updates, positioning shifts, and trends—highlighting what matters and why for my team's strategy.',
                NOW(),
                NOW()
            );
        """)

    manager.add_raw(insert_xray_templates)

    return manager


async def backwards():
    manager = MigrationManager(
        migration_id=ID,
        app_name="nebula",
        description=DESCRIPTION,
    )

    async def delete_xray_templates():
        # Delete the inserted templates (only Rumi templates with owner_id = NULL)
        await RawTable.raw(f"""
            DELETE FROM {xray_template_table_name} 
            WHERE owner_id IS NULL 
            AND title IN (
                'Meeting Action Items',
                'Project Status Updates',
                'Key Decisions & Outcomes',
                'Risk & Issue Tracker',
                'Budget & Financial Updates',
                'Weekly Team Sync Summary',
                'Monthly Goal Progress',
                'Client Feedback & Requirements',
                'Deadline & Milestone Alerts',
                'Process & Workflow Changes'
            );
        """)

    manager.add_raw(delete_xray_templates)

    return manager
