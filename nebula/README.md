# nebula

## Poetry

This project uses poetry. It's a modern dependency management
tool.

To run the project use this set of commands:

```bash
poetry config virtualenvs.create true --local
poetry env use 3.12.0
poetry install
poetry run python3 -m nebula
```

This will start the server on the configured host.

You can find swagger documentation at `/api/docs`.

You can read more about poetry here: https://python-poetry.org/

## Running Scripts and Workers

For instructions on how to run various scripts and workers in this project, including Temporal workflow scripts, see [SCRIPTS.md](SCRIPTS.md).

## Pi<PERSON>lo

We're using <PERSON><PERSON><PERSON> as postgresql orm. You can read more about piccolo here: https://piccolo-orm.readthedocs.io/en/latest/index.html.

Create models in `nebula/db/models` and run `poetry run piccolo migrations new nebula --auto` to create an empty migration.

To apply the new migrations run `poetry run piccolo migrations forwards nebula`.

To run tests:

```bash
pytest -vv .
```
